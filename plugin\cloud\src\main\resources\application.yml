app.version: ${project.version}

server:
  port: ${cloud_port:9102}

spring:
  application:
    name: ${project.name}
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration

logging:
  file:
    path: ./logs/${project.name}


mingdao:
  api:
    domain: ${md_domain:https://transfer.mingdao-info.com}
    SSO_TYPE: tenant
    SSO_URL: ${md_sso_url:https://transfer.mingdao-info.com/oauth}
    tasksinitpath: ${md_tasksinitpath:http://transfer.mingdao-info.com/ewstransfer}
    initPath: ${md_init_path:http://transfer.mingdao-info.com/ewstransfer/}
  edge:
    cache:
      type: ehcache
      ehcache:
        cacheName: ${spring.application.name}_${project.version}
        offheap: 100
        disk: 1000

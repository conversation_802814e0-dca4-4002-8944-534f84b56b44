package com.mingdao.edge.plugin.data.sync.config;


import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.mingdao.edge.core.api.biz.AbstractBizExchangeService;
import com.mingdao.edge.core.api.biz.BizExchangeService;
import com.mingdao.edge.core.api.biz.dto.BaseEvent;
import com.mingdao.edge.core.api.biz.dto.BizMessage;
import com.mingdao.edge.core.api.biz.enums.BizUnique;
import com.mingdao.edge.core.common.biz.event.EventHandlerUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@SofaService(interfaceType = BizExchangeService.class, uniqueId = BizUnique.DATA_COLLECTOR)
public class DataCollectExchangeService extends AbstractBizExchangeService<BizMessage<BaseEvent<?>>> {

    public DataCollectExchangeService() {
        super(BizUnique.DATA_COLLECTOR);
    }

    @Override
    public void handler(BizMessage<BaseEvent<?>> data) {
        BaseEvent<?> eventDTO = data.getData();
        EventHandlerUtils.fireEvent(eventDTO);
    }
}

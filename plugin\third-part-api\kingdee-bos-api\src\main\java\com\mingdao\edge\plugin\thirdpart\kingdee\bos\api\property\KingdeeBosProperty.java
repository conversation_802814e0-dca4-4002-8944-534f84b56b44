package com.mingdao.edge.plugin.thirdpart.kingdee.bos.api.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "kingdee", ignoreInvalidFields = true)
public class KingdeeBosProperty {
    private String acctID;
    private String appID;
    private String appSec;
    private String userName;
    private String lcid;
    private String serverUrl;
}

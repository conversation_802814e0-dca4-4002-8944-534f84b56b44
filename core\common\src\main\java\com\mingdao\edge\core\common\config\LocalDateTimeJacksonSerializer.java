package com.mingdao.edge.core.common.config;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.time.LocalDateTime;

/**
 * Jackson序列化处理器 - LocalDateTime
 *
 * <AUTHOR>
 * @date 2022/5/20
 * @since 1.1.0
 */
public class LocalDateTimeJacksonSerializer extends JsonSerializer<LocalDateTime> {

    @Override
    public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeNumber(LocalDateTimeUtil.toEpochMilli(value));
    }

}

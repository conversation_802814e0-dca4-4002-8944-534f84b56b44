<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mingdao.edge.plugin.api.mapper.DynamicSqlMapper">

    <!-- 执行查询SQL -->
    <select id="selectBySql" resultType="java.util.Map">
        ${sql}
    </select>

    <!-- 执行分页查询SQL -->
    <select id="selectPageBySql" resultType="java.util.Map">
        ${sql}
    </select>

    <!-- 执行插入SQL -->
    <insert id="insertBySql" useGeneratedKeys="true" keyProperty="params.id">
        ${sql}
    </insert>

    <!-- 执行更新SQL -->
    <update id="updateBySql">
        ${sql}
    </update>

    <!-- 执行删除SQL -->
    <delete id="deleteBySql">
        ${sql}
    </delete>

    <!-- Mapper XML -->
    <delete id="deleteByIds">
        DELETE FROM ${tableName}
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>

package com.mingdao.edge.plugin.api.data.sync.service;

import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.plugin.api.gateway.dto.DownMessage;
import com.mingdao.edge.plugin.api.gateway.dto.EventMessage;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
public interface DataSyncService {
    String getDataType();
    String getUpTopic();
    /**
     * 初始化，获取服务端配置
     */
    void init(JSONObject taskInfo);
    void clearData();
    /**
     * 执行同步业务
     */
    void getData();

    void sendToDataConvertor(Long id, String upTopic);
    /**
     * 根据ID获取数据库行，然后转换为上传的数据
     * @param id
     * @return
     */
    JSONObject getPraseData(Long id);
    /**
     * 传输数据
     */
    void transferData(EventMessage<JSONObject> message);
    /**
     * 传输数据状态回调
     * @param downMessage
     */
    void transferDataCallback(DownMessage<?> downMessage);


    /**
     * 重传失败的数据
     */
    void transFailData();

    /**
     * 获取最后同步时间
     * @return
     */
    Long getLastDataTime();
    void updateLastTime(long time);
}

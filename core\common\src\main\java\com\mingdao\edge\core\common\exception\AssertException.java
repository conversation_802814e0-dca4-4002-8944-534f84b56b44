package com.mingdao.edge.core.common.exception;

import com.mingdao.edge.core.common.dto.HttpResponseStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 断言异常
 */
@Setter
@Getter
@NoArgsConstructor
public class AssertException extends ApplicationException {

    private static final long serialVersionUID = 1L;

    /**
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     */
    public AssertException(String msg, Object... msgParams) {
        super(HttpResponseStatus.ERROR, null, msg, msgParams);
    }

    /**
     * @param cause 原异常
     */
    public AssertException(Throwable cause) {
        super(HttpResponseStatus.ERROR, cause, null);
    }

    /**
     * @param cause     原异常
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     */
    public AssertException(Throwable cause, String msg, Object... msgParams) {
        super(HttpResponseStatus.ERROR, cause, msg, msgParams);
    }
}

package com.mingdao.edge.plugin.cloud.api;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alipay.sofa.runtime.api.annotation.SofaReference;
import com.mingdao.edge.core.common.constants.RequestResult;
import com.mingdao.edge.core.common.dto.BaseResponse;
import com.mingdao.edge.core.common.exception.ErrorCode;
import com.mingdao.edge.core.common.exception.InvalidTokenException;
import com.mingdao.edge.core.common.http.RequestUtils;
import com.mingdao.edge.plugin.api.gateway.OAuthApi;
import com.mingdao.edge.plugin.cloud.property.CloudApiProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import javax.annotation.Resource;
import java.util.LinkedHashMap;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Slf4j
public abstract class AbstractApiImpl {

    @Resource
    protected CloudApiProperties cloudApiProperties;
    @SofaReference
    protected OAuthApi oAuthApi;

    protected <T> BaseResponse<T> postWithToken(String url, Object params) {
        return postWithToken(url, params, false);
    }

    protected <T> BaseResponse<T> postWithToken(String url, Object params, boolean isRetry) {
        String accessToken = oAuthApi.getAccessToken();
        if (StrUtil.isBlank(accessToken)) {
            throw new InvalidTokenException(ErrorCode.ERROR401);
        }
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        httpHeaders.add("Cookie", accessToken);
        httpHeaders.add("Token", accessToken);
        try {
            ResponseEntity<org.springframework.core.io.Resource> responseEntity = RequestUtils.postByTypeReference(url, params, httpHeaders, new ParameterizedTypeReference<org.springframework.core.io.Resource>() {});
            MediaType contentType = responseEntity.getHeaders().getContentType();
            org.springframework.core.io.Resource resource = responseEntity.getBody();
            BaseResponse<T> response = null;
            assert resource != null;
            TypeReference<BaseResponse<T>> t = new TypeReference<BaseResponse<T>>() {};
            try {
                byte[] fileData = IoUtil.readBytes(resource.getInputStream());
                if (fileData != null) {
                    String jsonStr = new String(fileData);
                    response = JSON.parseObject(jsonStr, t);
                }
            } catch (Exception e) {
                log.warn("解析响应体失败，尝试直接转换: {}", e.getMessage());
                Object body = responseEntity.getBody();
                if (body instanceof LinkedHashMap) {
                    response = new JSONObject((LinkedHashMap) body).toJavaObject(t);
                }
            }

            if (response == null) {
                return null;
            }
            if (RequestResult.RES_SUCCESS.equals(response.getRes())) {
                return response;
            } else if (RequestResult.RES_TOKEN_EXPIRED.equals(response.getRes())) {
                if (!isRetry) {
                    oAuthApi.removeAccessToken();
                    return postWithToken(url, params, true);
                }
            }
            return response;
        } catch (Exception e) {
            log.error("[请求云端]异常: " + e.getMessage(), e);
        }
        if (!isRetry) {
            oAuthApi.removeAccessToken();
            return postWithToken(url, params, true);
        }
        return null;
    }

    protected <T, P> BaseResponse<T> postWithoutAuth(String url, P params, TypeReference<BaseResponse<T>> t) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        try {
            ResponseEntity<org.springframework.core.io.Resource> responseEntity = RequestUtils.postByTypeReference(url, params, httpHeaders, new ParameterizedTypeReference<org.springframework.core.io.Resource>() {});
            MediaType contentType = responseEntity.getHeaders().getContentType();
            org.springframework.core.io.Resource resource = responseEntity.getBody();
            BaseResponse<T> response = null;
            assert resource != null;
            try {
                byte[] fileData = IoUtil.readBytes(resource.getInputStream());
                if (fileData != null) {
                    String jsonStr = new String(fileData);
                    response = JSON.parseObject(jsonStr, t);
                }
            } catch (Exception e) {
                log.warn("解析响应体失败，尝试直接转换: {}", e.getMessage());
                Object body = responseEntity.getBody();
                if (body instanceof LinkedHashMap) {
                    response = new JSONObject((LinkedHashMap) body).toJavaObject(t);
                }
            }
            return response;
        } catch (Exception e) {
            log.error("[请求云端]异常: " + e.getMessage(), e);
        }
        return null;
    }
}

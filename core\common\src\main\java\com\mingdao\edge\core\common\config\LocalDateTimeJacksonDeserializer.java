package com.mingdao.edge.core.common.config;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * Jackson反序列化处理器 - LocalDateTime
 *
 * <AUTHOR>
 * @date 2022/5/20
 * @since 1.1.0
 */
public class LocalDateTimeJacksonDeserializer extends JsonDeserializer<LocalDateTime> {

    @Override
    public LocalDateTime deserialize(JsonParser jsonParser, DeserializationContext ctx) throws IOException {

        return Optional.ofNullable(jsonParser.getValueAsString())
                .filter(StrUtil::isNotBlank)
                .map(valueStr -> {
                    long timestamp = Long.parseLong(valueStr);
                    Assert.notNull(timestamp, "{}转为Long型失败！", valueStr);
                    return LocalDateTimeUtil.of(timestamp);
                })
                .orElse(null);
    }
}

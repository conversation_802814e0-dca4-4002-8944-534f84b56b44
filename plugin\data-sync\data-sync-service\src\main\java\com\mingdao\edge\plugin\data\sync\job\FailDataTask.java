package com.mingdao.edge.plugin.data.sync.job;

import com.mingdao.edge.plugin.api.data.sync.service.DataSyncService;
import com.mingdao.edge.plugin.data.sync.holder.DataSyncReferenceHolder;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-28
 */
@Component
public class FailDataTask {

    @Resource
    private DataSyncReferenceHolder dataSyncReferenceHolder;

    @Scheduled(fixedRateString = "${mingdao.check_fail_data_interval:3600_000}")
    public void checkData() {
        List<DataSyncService> handlerList = dataSyncReferenceHolder.getAllService();
        if (handlerList != null && !handlerList.isEmpty()) {
            handlerList.forEach(DataSyncService::transFailData);
        }
    }
}

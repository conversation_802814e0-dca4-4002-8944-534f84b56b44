package com.mingdao.edge.core.mqtt.config;

import com.mingdao.edge.core.common.context.SpringContextUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.mqttv5.client.IMqttMessageListener;
import org.eclipse.paho.mqttv5.client.MqttCallback;
import org.eclipse.paho.mqttv5.client.MqttClient;
import org.eclipse.paho.mqttv5.client.MqttConnectionOptions;
import org.eclipse.paho.mqttv5.client.persist.MemoryPersistence;
import org.eclipse.paho.mqttv5.common.MqttException;
import org.eclipse.paho.mqttv5.common.MqttMessage;
import org.eclipse.paho.mqttv5.common.MqttSubscription;
import org.springframework.beans.BeansException;

import java.util.Arrays;

/**
 * MQTT客户端配置信息
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Slf4j
@Getter
@Setter
public class MqttClientInfo {
    /**
     * MQTT代理服务器地址
     */
    public String broker;

    /**
     * MQTT用户名
     */
    public String userName;

    /**
     * MQTT客户端ID
     */
    public String clientId;

    /**
     * MQTT密码
     */
    public String password;

    /**
     * 订阅的主题列表 (兼容旧版本)
     */
    public String[] topics;

    /**
     * MQTT订阅配置 (新版本)
     */
    public MqttSubscription[] subscriptions;

    /**
     * MQTT消息监听器 (新版本)
     */
    public IMqttMessageListener[] messageListeners;

    /**
     * MQTT回调处理器列表
     */
    public MqttCallback callbackHandler;


    public void createMqttClient() throws MqttException {

        String beanName = "mqttClient";
        MqttClient client = getMqttClient(beanName);
        if (client != null) {
            SpringContextUtils.unregisterBean(beanName);
            log.info("注销 {}", beanName);
        }

        MemoryPersistence persistence = new MemoryPersistence();
        client = new MqttClient(this.getBroker(), this.getClientId(), persistence);
        // MQTT v5 连接选项
        MqttConnectionOptions connOpts = new MqttConnectionOptions();
        connOpts.setUserName(this.getUserName());
        connOpts.setPassword(this.getPassword().getBytes());
        // 保留会话
        connOpts.setCleanStart(false);

        connOpts.setAutomaticReconnect(true);
        connOpts.setConnectionTimeout(10);
        connOpts.setKeepAliveInterval(60);

        MqttMessage mqttMessage = new MqttMessage();
        mqttMessage.setQos(1);
        mqttMessage.setPayload(this.clientId.getBytes());
        connOpts.setWill("edge/offline", mqttMessage);

        // 设置回调
        client.setCallback(this.callbackHandler);


        // 建立连接
        client.connect(connOpts);

        // 新订阅方式
        if (this.getSubscriptions() != null && this.getMessageListeners() != null
                && this.getSubscriptions().length > 0
                && this.getSubscriptions().length == this.getMessageListeners().length) {
            client.subscribe(this.getSubscriptions(), this.getMessageListeners());
        } else if (this.getTopics() != null && this.getTopics().length > 0) {
            // 兼容旧方式
            int[] qos = new int[this.getTopics().length];
            Arrays.fill(qos, 1);
            client.subscribe(this.getTopics(), qos);
        }

        SpringContextUtils.registerBean(beanName, client);
    }

    private MqttClient getMqttClient(String beanName) {
        MqttClient client = null;
        try {
            client = SpringContextUtils.getBean(beanName);
        } catch (BeansException ignored) {
            log.warn("注销 {} 异常 {}", beanName, ignored.getMessage());
        }
        return client;
    }

}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.alipay.sofa</groupId>
        <artifactId>sofaboot-dependencies</artifactId>
        <version>3.13.0</version>
        <relativePath/>
    </parent>

    <groupId>com.mingdao.edge</groupId>
    <artifactId>bom</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <description>Edge 公共版本控制</description>

    <properties>
        <revision>1.0.0</revision>
        <spring.boot.version>2.7.0</spring.boot.version>
        <!--Sofa-->
        <sofa.ark.version>2.3.1</sofa.ark.version>
        <sofa.boot.version>3.13.0</sofa.boot.version>
        <hessian.version>4.0.4</hessian.version>
        <!--Tools-->
        <slf4j.version>1.7.21</slf4j.version>
        <fastjson.version>1.2.48.sec10</fastjson.version>
        <commons-lang3.version>3.10</commons-lang3.version>
        <hutool.version>5.8.4</hutool.version>
        <mina.version>2.2.3</mina.version>
        <ehcache.version>3.10.0</ehcache.version>
        <okhttp.version>4.9.2</okhttp.version>
        <mybatis-plus.version>3.5.1</mybatis-plus.version>
        <httpclient.version>4.5.13</httpclient.version>
        <artemis-http-client.version>1.1.3</artemis-http-client.version>
        <h2.version>2.3.232</h2.version>
        <axon.version>4.5.10</axon.version>
        <druid.version>1.1.22</druid.version>
        <mssql-jdbc.version>12.4.2.jre11</mssql-jdbc.version>
        <disruptor.version>3.4.4</disruptor.version>
        <mqttv5.version>1.2.5</mqttv5.version>
        <pulsar.version>4.0.6</pulsar.version>
        <!-- Build args -->
        <module.install.skip>false</module.install.skip>
        <module.deploy.skip>true</module.deploy.skip>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- sofa-ark-plugin -->
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>web-ark-plugin</artifactId>
                <version>${sofa.ark.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-ark-spi</artifactId>
                <version>${sofa.ark.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-ark-api</artifactId>
                <version>${sofa.ark.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>healthcheck-sofa-boot-starter</artifactId>
                <version>${sofa.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-actuator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>runtime-sofa-boot-starter</artifactId>
                <version>${sofa.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>runtime-sofa-boot-plugin</artifactId>
                <version>${sofa.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-ark-all</artifactId>
                <version>${sofa.ark.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-classic</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-ark-compatible-springboot2</artifactId>
                <version>${sofa.ark.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-ark-springboot-starter</artifactId>
                <version>${sofa.ark.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alipay.sofa</groupId>
                        <artifactId>sofa-ark-compatible-springboot1</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-actuator</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-web</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>hessian</artifactId>
                <version>${hessian.version}</version>
            </dependency>
            <!-- end sofa-ark-plugin -->

            <!-- tools -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.mina</groupId>
                <artifactId>mina-core</artifactId>
                <version>${mina.version}</version>
            </dependency>
            <dependency>
                <groupId>org.ehcache</groupId>
                <artifactId>ehcache</artifactId>
                <version>${ehcache.version}</version>
            </dependency>
            <!-- okhttp -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <!-- okhttp 调试日志 -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <!-- okhttp-digest -->
            <dependency>
                <groupId>com.burgstaller</groupId>
                <artifactId>okhttp-digest</artifactId>
                <version>2.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>31.0.1-jre</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>${h2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hikvision.ga</groupId>
                <artifactId>artemis-http-client</artifactId>
                <version>${artemis-http-client.version}</version>
            </dependency>
            <!-- 基础Druid依赖 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>1.4.1</version>
            </dependency>
            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.paho</groupId>
                <artifactId>org.eclipse.paho.mqttv5.client</artifactId>
                <version>${mqttv5.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pulsar</groupId>
                <artifactId>pulsar-client</artifactId>
                <version>${pulsar.version}</version>
            </dependency>
            <!-- end Tools -->

            <!--<dependency>-->
            <!--    <groupId>com.mingdao.edge</groupId>-->
            <!--    <artifactId>mapper-api</artifactId>-->
            <!--    <version>${revision}</version>-->
            <!--</dependency>-->

            <!--<dependency>-->
            <!--    <groupId>com.mingdao.core.log</groupId>-->
            <!--    <artifactId>log-base</artifactId>-->
            <!--    <version>${yhlz-iot-core-log.version}</version>-->
            <!--</dependency>-->

            <!--<dependency>-->
            <!--    <groupId>com.mingdao.edge</groupId>-->
            <!--    <artifactId>yhlz-iot-edge-log</artifactId>-->
            <!--    <version>${revision}</version>-->
            <!--</dependency>-->

            <dependency>
                <groupId>org.axonframework</groupId>
                <artifactId>axon-spring-boot-starter</artifactId>
                <version>${axon.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
</project>

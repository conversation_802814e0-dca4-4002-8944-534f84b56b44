package com.mingdao.edge.plugin.gateway.config;

import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.core.api.biz.BizCache;
import com.mingdao.edge.plugin.api.gateway.ServerApi;
import com.mingdao.edge.plugin.gateway.holder.GatewayDownHandlerHolder;
import com.mingdao.edge.plugin.gateway.service.StartUpService;
import com.mingdao.edge.plugin.gateway.util.BizFileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Slf4j
@Component
public class StartupApplicationListener {

    @Resource
    private BizCache bizCache;
    @Resource
    private GatewayDownHandlerHolder gatewayDownHandlerHolder;

    @Resource
    private StartUpService startUpService;
    @Resource
    private ServerApi serverApi;

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        try {
            log.info("[DEBUG] list all cache {}", JSONObject.toJSONString(bizCache.listAll()));


            gatewayDownHandlerHolder.init();
            BizFileUtil.deleteAllUninstallBizDir();

            startUpService.initMqtt();
            startUpService.initBizInfo();

        } catch (Exception e) {
            log.error("gateway AfterBizStartupEventHandler 异常" + e.getMessage(), e);
        }
    }
}

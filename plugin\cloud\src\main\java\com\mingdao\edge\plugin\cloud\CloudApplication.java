package com.mingdao.edge.plugin.cloud;

import com.mingdao.edge.core.common.http.RestTemplateConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * description DataSyncApplication
 *
 * <AUTHOR>
 * @since 2025-06-06 17:17:39
 */
@EnableAsync
@SpringBootApplication(scanBasePackages = {"com.mingdao.edge"}, scanBasePackageClasses = {RestTemplateConfig.class})
public class CloudApplication {
    public static void main(String[] args) {
        SpringApplication.run(CloudApplication.class, args);
    }
}

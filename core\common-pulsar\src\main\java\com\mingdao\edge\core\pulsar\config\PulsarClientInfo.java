package com.mingdao.edge.core.pulsar.config;

import com.mingdao.edge.core.common.context.SpringContextUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.*;
import org.springframework.beans.BeansException;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * Pulsar客户端配置信息
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Slf4j
@Getter
@Setter
public class PulsarClientInfo {

    /**
     * Pulsar服务地址
     */
    private String serviceUrl;

    /**
     * 认证Token（可选）
     */
    private String authToken;

    /**
     * 客户端名称
     */
    private String clientName;

    /**
     * 连接超时时间（秒）
     */
    private int connectionTimeoutSeconds = 30;

    /**
     * 操作超时时间（秒）
     */
    private int operationTimeoutSeconds = 30;

    /**
     * 是否启用TLS
     */
    private boolean tlsEnabled = false;
    private Integer ioThreads = 4;
    private Integer listenerThreads = 6;

    private Map<String, String> consumerBeanMap = new HashMap<>();

    // 缓存消费者
    private final Map<String, Consumer<byte[]>> consumers = new ConcurrentHashMap<>();

    /**
     * 创建Pulsar客户端
     */
    public void createPulsarClient() throws PulsarClientException {
        String beanName = "pulsarClient";
        PulsarClient client = getType(beanName);
        if (client != null) {
            SpringContextUtils.unregisterBean(beanName);
            log.info("注销 {}", beanName);
        }


        // 创建客户端
        ClientBuilder clientBuilder = PulsarClient.builder()
                .serviceUrl(this.getServiceUrl())
                .listenerThreads(this.getListenerThreads())
                .ioThreads(this.getIoThreads())
                .connectionTimeout(this.getConnectionTimeoutSeconds(), TimeUnit.SECONDS)
                .operationTimeout(this.getConnectionTimeoutSeconds(), TimeUnit.SECONDS)
                .keepAliveInterval(this.getConnectionTimeoutSeconds(), TimeUnit.SECONDS);

        clientBuilder.authentication(AuthenticationFactory.token(this.authToken));





        // 创建客户端
        client = clientBuilder.build();

        // 注册到Spring容器
        SpringContextUtils.registerBean(beanName, client);
    }

    private <T> T getType(String beanName) {
        T t = null;
        try {
            t = SpringContextUtils.getBean(beanName);
        } catch (BeansException ignored) {
            log.warn("获取 {} 异常 {}", beanName, ignored.getMessage());
        }
        return t;
    }
}

package com.mingdao.edge.plugin.data.sync.service.impl;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alipay.sofa.runtime.api.annotation.SofaReference;
import com.mingdao.edge.core.common.cache.EdgeCacheManager;
import com.mingdao.edge.core.common.exception.ApplicationException;
import com.mingdao.edge.plugin.api.cloud.EwsApi;
import com.mingdao.edge.core.common.dto.BaseResponse;
import com.mingdao.edge.plugin.data.sync.base.constants.CacheKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Slf4j
@Service
public class CacheService {
    @Resource
    private EdgeCacheManager edgeCacheManager;
    @SofaReference
    private EwsApi ewsApi;

    private JSONArray initTaskInfo() {
        log.debug("[初始化同步任务]开始");
        BaseResponse<JSONArray> taskListRes = ewsApi.getTaskList();
        if (taskListRes == null || ObjectUtil.isEmpty(taskListRes)
                || ArrayUtil.isEmpty(taskListRes.getData())) {
            throw new ApplicationException("[数据同步][任务信息]模版未正常初始化");
        }
        JSONArray taskList = taskListRes.getData();
        if (!ArrayUtil.isEmpty(taskList)) {
            edgeCacheManager.set(CacheKey.SYNC_TASK_LIST, taskList, 5 * 60);
            log.debug("[初始化同步]完成初始化 {}", taskList);
        } else {
            log.info("[初始化同步]缺少任务信息");
        }
        return taskList;
    }

    public JSONArray getTaskInfo(boolean clearCache) {
        JSONArray result = null;
        if (clearCache) {
            edgeCacheManager.remove(CacheKey.SYNC_TASK_LIST);
        } else {
            result = edgeCacheManager.get(CacheKey.SYNC_TASK_LIST, JSONArray.class);
        }
        if (result == null || ArrayUtil.isEmpty(result)) {
            result = this.initTaskInfo();
        }
        return result;
    }
}

package com.mingdao.edge.core.pulsar.consumer;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.mingdao.edge.core.common.context.SpringContextUtils;
import com.mingdao.edge.core.pulsar.config.ConsumerProperty;
import com.mingdao.edge.core.pulsar.config.PulsarClientProperty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.*;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.*;

/**
 * Pulsar消费者工厂
 * 负责创建和管理Pulsar消费者
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PulsarConsumerFactory {

    private final ConsumerProperty consumerProperty;
    private final PulsarClientProperty pulsarClientProperty;

    // 缓存消费者
    private final Map<String, Consumer<byte[]>> consumers = new ConcurrentHashMap<>();
    // 每个消费者一个执行器，用于并行处理
    private final Map<String, ExecutorService> executors = new ConcurrentHashMap<>();

    /**
     * 创建消费者
     *
     * @param topic            主题名称
     * @param subscription     订阅名称
     * @param subscriptionType 订阅类型
     * @param messageListener  消息监听器
     * @return 消费者
     */
    public Consumer<byte[]> createConsumer(String topic, String subscription,
                                           SubscriptionType subscriptionType,
                                           MessageListener<byte[]> messageListener) {
        try {
            String consumerKey = buildConsumerKey(topic, subscription);

            // 检查是否已存在
            if (consumers.containsKey(consumerKey)) {
                //log.warn("消费者已存在: {}", consumerKey);
                return consumers.get(consumerKey);
            }

            log.info("创建Pulsar消费者，主题: {}, 订阅: {}, 类型: {}", topic, subscription, subscriptionType);

            PulsarClient pulsarClient = SpringContextUtils.getBean(PulsarClient.class);
            ConsumerBuilder<byte[]> consumerBuilder = pulsarClient.newConsumer()
                    .topic(topic)
                    .subscriptionName(subscription)
                    .subscriptionType(subscriptionType)
                    .receiverQueueSize(consumerProperty.getReceiverQueueSize())
                    .ackTimeout(consumerProperty.getAckTimeoutMs(), TimeUnit.MILLISECONDS)
                    .negativeAckRedeliveryDelay(consumerProperty.getNegativeAckRedeliveryDelayMs(), TimeUnit.MILLISECONDS);

            // 配置死信队列
            if (!StrUtil.isEmpty(consumerProperty.getDeadLetterTopic())) {
                consumerBuilder.deadLetterPolicy(DeadLetterPolicy.builder()
                        .maxRedeliverCount(consumerProperty.getMaxRedeliverCount())
                        .deadLetterTopic(consumerProperty.getDeadLetterTopic())
                        .retryLetterTopic(consumerProperty.getRetryTopic())
                        .build());
            }

            // 配置批量接收
            if (consumerProperty.isBatchReceiveEnabled()) {
                consumerBuilder.batchReceivePolicy(BatchReceivePolicy.builder()
                        .maxNumMessages(consumerProperty.getBatchReceiveMaxMessages())
                        .timeout((int) consumerProperty.getBatchReceiveTimeoutMs(), TimeUnit.MILLISECONDS)
                        .build());
            }

            // 包装监听器以支持并行处理
            MessageListener<byte[]> wrappedListener = wrapListenerWithExecutor(consumerKey, consumerProperty, messageListener);

            Consumer<byte[]> consumer = consumerBuilder.messageListener(wrappedListener).subscribe();

            // 缓存消费者和执行器
            consumers.put(consumerKey, consumer);

            log.info("成功创建Pulsar消费者，主题: {}, 订阅: {}", topic, subscription);
            return consumer;

        } catch (Exception e) {
            log.error("创建Pulsar消费者失败，主题: {}, 订阅: {}", topic, subscription, e);
        }
        return null;
    }

    /**
     * 创建消费者
     *
     * @param namespace        主题名称
     * @param subscription     订阅名称
     * @param subscriptionType 订阅类型
     * @param messageListener  消息监听器
     * @return 消费者
     */
    public void createPatternConsumer(String tenantName,
                                      String namespace,
                                      String subscription,
                                      SubscriptionType subscriptionType,
                                      MessageListener<byte[]> messageListener) {
        try {
            String consumerKey = buildConsumerKey(namespace, subscription);
            // 检查是否已存在
            if (consumers.containsKey(consumerKey)) {
                //log.warn("消费者已存在: {}", consumerKey);
                return;
            }

            log.info("创建Pulsar消费者 {}，主题: {}, 订阅: {}, 类型: {}", tenantName, namespace, subscription, subscriptionType);

            String topicPattern = StrUtil.format("persistent://{}/{}/.*", tenantName, namespace);

            PulsarClient pulsarClient = SpringContextUtils.getBean(PulsarClient.class);
            ConsumerBuilder<byte[]> consumerBuilder = pulsarClient.newConsumer()
                    .topicsPattern(topicPattern)
                    .subscriptionName(subscription)
                    .subscriptionType(subscriptionType)
                    .receiverQueueSize(consumerProperty.getReceiverQueueSize())
                    .ackTimeout(consumerProperty.getAckTimeoutMs(), TimeUnit.MILLISECONDS)
                    .negativeAckRedeliveryDelay(consumerProperty.getNegativeAckRedeliveryDelayMs(), TimeUnit.MILLISECONDS);

            // 配置死信队列
            if (!StrUtil.isEmpty(consumerProperty.getDeadLetterTopic())) {
                consumerBuilder.deadLetterPolicy(DeadLetterPolicy.builder()
                        .maxRedeliverCount(consumerProperty.getMaxRedeliverCount())
                        .deadLetterTopic(consumerProperty.getDeadLetterTopic())
                        .retryLetterTopic(consumerProperty.getRetryTopic())
                        .build());
            }

            // 配置批量接收
            if (consumerProperty.isBatchReceiveEnabled()) {
                consumerBuilder.batchReceivePolicy(BatchReceivePolicy.builder()
                        .maxNumMessages(consumerProperty.getBatchReceiveMaxMessages())
                        .timeout((int) consumerProperty.getBatchReceiveTimeoutMs(), TimeUnit.MILLISECONDS)
                        .build());
            }

            // 包装监听器为并行处理
            MessageListener<byte[]> wrappedListener = wrapListenerWithExecutor(consumerKey, consumerProperty, messageListener);

            Consumer<byte[]> consumer = consumerBuilder.messageListener(wrappedListener).subscribe();

            // 缓存消费者
            consumers.put(consumerKey, consumer);

            log.info("成功创建Pulsar消费者，主题: {}, 订阅: {}", namespace, subscription);
            log.info("state: {}", JSON.toJSONString(consumer.isConnected()));

        } catch (Exception e) {
            log.error("创建Pulsar消费者失败，主题: {}, 订阅: {}", namespace, subscription, e);
        }
    }

    /**
     * 关闭指定消费者
     *
     * @param topic        主题名称
     * @param subscription 订阅名称
     */
    public void closeConsumer(String topic, String subscription) {
        String consumerKey = buildConsumerKey(topic, subscription);
        Consumer<byte[]> consumer = consumers.remove(consumerKey);
        ExecutorService executor = executors.remove(consumerKey);

        if (executor != null) {
            shutdownExecutor(consumerKey, executor);
        }
        if (consumer != null) {
            try {
                consumer.close();
                log.info("关闭消费者，主题: {}, 订阅: {}", topic, subscription);
            } catch (Exception e) {
                log.warn("关闭消费者失败，主题: {}, 订阅: {}", topic, subscription, e);
            }
        }
    }


    private MessageListener<byte[]> wrapListenerWithExecutor(String consumerKey,
                                                             ConsumerProperty consumerProperty,
                                                             MessageListener<byte[]> delegate) {
        int concurrency = Math.max(1, consumerProperty.getConcurrency());
        if (concurrency <= 1) {
            // 不启用并行，直接返回原监听器
            return delegate;
        }
        int queueCapacity = Math.max(1, consumerProperty.getQueueCapacity());
        BlockingQueue<Runnable> queue = new ArrayBlockingQueue<>(queueCapacity);
        ThreadFactory threadFactory = r -> {
            Thread t = new Thread(r);
            t.setName("pulsar-consumer-" + consumerKey + "-worker-" + t.getId());
            t.setDaemon(true);
            return t;
        };
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                concurrency,
                concurrency,
                60L,
                TimeUnit.SECONDS,
                queue,
                threadFactory,
                new ThreadPoolExecutor.CallerRunsPolicy() // 背压：队列满时在调用线程执行
        );
        executors.put(consumerKey, executor);

        return (consumer, message) -> {
            executor.execute(() -> {
                try {
                    delegate.received(consumer, message);
                } catch (Throwable ex) {
                    log.error("并行处理消息异常, consumerKey={}, msgId={}", consumerKey, message.getMessageId(), ex);
                }
            });
        };
    }


    /**
     * 关闭所有消费者
     */
    public void closeAllConsumers() {
        log.info("关闭所有消费者，共 {} 个", consumers.size());

        consumers.forEach((key, consumer) -> {
            try {
                consumer.close();
                log.info("关闭消费者: {}", key);
            } catch (Exception e) {
                log.warn("关闭消费者失败: {}", key, e);
            }
        });

        consumers.clear();

        executors.forEach(this::shutdownExecutor);
        executors.clear();
    }

    private void shutdownExecutor(String key, ExecutorService executor) {
        executor.shutdown();
        try {
            if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            executor.shutdownNow();
        }
        log.info("关闭消费者线程池: {}", key);
    }

    /**
     * 获取消费者状态
     */
    public String getConsumerStatus(String topic, String subscription) {
        String consumerKey = buildConsumerKey(topic, subscription);
        Consumer<byte[]> consumer = consumers.get(consumerKey);

        if (consumer == null) {
            return "消费者不存在";
        }

        return String.format("消费者 %s - 主题: %s, 订阅: %s, 连接状态: %s",
                consumer.getConsumerName(), consumer.getTopic(),
                consumer.getSubscription(), consumer.isConnected() ? "已连接" : "未连接");
    }

    /**
     * 获取所有消费者键
     */
    public Set<String> getAllConsumerKeys() {
        return Collections.unmodifiableSet(consumers.keySet());
    }

    /**
     * 构建消费者键
     */
    private String buildConsumerKey(String topic, String subscription) {
        return topic + "#" + subscription;
    }

    @PreDestroy
    public void destroy() {
        closeAllConsumers();
    }
}

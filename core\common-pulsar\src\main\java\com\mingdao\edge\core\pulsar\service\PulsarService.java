package com.mingdao.edge.core.pulsar.service;

import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.core.pulsar.utils.PulsarClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.MessageId;
import org.apache.pulsar.client.api.Producer;
import org.apache.pulsar.client.api.TypedMessageBuilder;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Pulsar消息发布服务
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Slf4j
@Service
public class PulsarService {

    /**
     * 同步发送消息
     *
     * @param topic   主题
     * @param payload 消息内容
     * @param <T>     消息类型
     * @return 消息ID
     */
    public <T> MessageId send(String topic, T payload) {
        return send(topic, payload, null);
    }

    /**
     * 同步发送消息（带消息键）
     *
     * @param topic   主题
     * @param payload 消息内容
     * @param key     消息键
     * @param <T>     消息类型
     * @return 消息ID
     */
    public <T> MessageId send(String topic, T payload, String key) {
        try {
            Producer<byte[]> producer = PulsarClientUtil.getOrCreateProducer(topic);

            TypedMessageBuilder<byte[]> messageBuilder = producer.newMessage()
                    .value(convertToBytes(payload));

            // 设置消息键
            if (key != null && !key.isEmpty()) {
                messageBuilder.key(key);
            }

            MessageId messageId = messageBuilder.send();
            log.debug("Pulsar消息发送成功: topic={}, messageId={}", topic, messageId);
            return messageId;

        } catch (Exception e) {
            log.error("Pulsar消息发送失败: topic={}, error={}", topic, e.getMessage(), e);
            throw new RuntimeException("发送消息失败", e);
        }
    }

    /**
     * 异步发送消息
     *
     * @param topic   主题
     * @param payload 消息内容
     * @param <T>     消息类型
     * @return CompletableFuture<MessageId>
     */
    public <T> CompletableFuture<MessageId> sendAsync(String topic, T payload) {
        return sendAsync(topic, payload, null);
    }

    /**
     * 异步发送消息（带消息键）
     *
     * @param topic   主题
     * @param payload 消息内容
     * @param key     消息键
     * @param <T>     消息类型
     * @return CompletableFuture<MessageId>
     */
    public <T> CompletableFuture<MessageId> sendAsync(String topic, T payload, String key) {
        try {
            Producer<byte[]> producer = PulsarClientUtil.getOrCreateProducer(topic);

            TypedMessageBuilder<byte[]> messageBuilder = producer.newMessage()
                    .value(convertToBytes(payload));

            // 设置消息键
            if (key != null && !key.isEmpty()) {
                messageBuilder.key(key);
            }

            CompletableFuture<MessageId> future = messageBuilder.sendAsync();

            // 添加回调处理
            future.whenComplete((messageId, throwable) -> {
                if (throwable != null) {
                    log.error("Pulsar异步消息发送失败: topic={}, error={}", topic, throwable.getMessage(), throwable);
                } else {
                    log.debug("Pulsar异步消息发送成功: topic={}, messageId={}", topic, messageId);
                }
            });

            return future;

        } catch (Exception e) {
            log.error("Pulsar异步消息发送失败: topic={}, error={}", topic, e.getMessage(), e);
            CompletableFuture<MessageId> failedFuture = new CompletableFuture<>();
            failedFuture.completeExceptionally(e);
            return failedFuture;
        }
    }

    /**
     * 发送延迟消息
     *
     * @param topic   主题
     * @param payload 消息内容
     * @param delay   延迟时间
     * @param unit    时间单位
     * @param <T>     消息类型
     * @return 消息ID
     */
    public <T> MessageId sendDelayed(String topic, T payload, long delay, TimeUnit unit) {
        return sendDelayed(topic, payload, null, delay, unit);
    }

    /**
     * 发送延迟消息（带消息键）
     *
     * @param topic   主题
     * @param payload 消息内容
     * @param key     消息键
     * @param delay   延迟时间
     * @param unit    时间单位
     * @param <T>     消息类型
     * @return 消息ID
     */
    public <T> MessageId sendDelayed(String topic, T payload, String key, long delay, TimeUnit unit) {
        try {
            Producer<byte[]> producer = PulsarClientUtil.getOrCreateProducer(topic);

            TypedMessageBuilder<byte[]> messageBuilder = producer.newMessage()
                    .value(convertToBytes(payload))
                    .deliverAfter(delay, unit);

            // 设置消息键
            if (key != null && !key.isEmpty()) {
                messageBuilder.key(key);
            }

            MessageId messageId = messageBuilder.send();
            log.debug("Pulsar延迟消息发送成功: topic={}, messageId={}, delay={}{}",
                     topic, messageId, delay, unit.toString().toLowerCase());
            return messageId;

        } catch (Exception e) {
            log.error("Pulsar延迟消息发送失败: topic={}, error={}", topic, e.getMessage(), e);
            throw new RuntimeException("发送延迟消息失败", e);
        }
    }

    /**
     * 将对象转换为字节数组
     */
    private <T> byte[] convertToBytes(T payload) {
        if (payload instanceof String) {
            return ((String) payload).getBytes(StandardCharsets.UTF_8);
        } else if (payload instanceof byte[]) {
            return (byte[]) payload;
        } else {
            return JSONObject.toJSONString(payload).getBytes(StandardCharsets.UTF_8);
        }
    }
}

package com.mingdao.edge.plugin.data.sync.config;

import com.mingdao.edge.core.common.exception.Assert;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;

import java.util.concurrent.ScheduledFuture;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@Configuration
public class DynamicTaskScheduler implements SchedulingConfigurer {

    private ScheduledTaskRegistrar taskRegistrar;

    @Override
    public void configureTasks(@NotNull ScheduledTaskRegistrar taskRegistrar) {
        this.taskRegistrar = taskRegistrar;
    }

    /**
     * 添加动态任务
     * @param taskId 任务ID
     * @param task 任务逻辑
     * @param cron cron表达式
     */
    public ScheduledFuture<?> buildTask(String taskId, Runnable task, String cron) {
        Assert.notNull(taskRegistrar.getScheduler(), "任务注册器为空");
        ScheduledFuture<?> result = taskRegistrar.getScheduler()
                .schedule(task,triggerContext -> new CronTrigger(cron).nextExecutionTime(triggerContext));
        log.info("新增数据同步任务: " + taskId);
        return result;
    }
}

package com.mingdao.edge.plugin.data.sync.handler.cmd;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alipay.sofa.runtime.api.annotation.SofaReference;
import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.mingdao.edge.core.common.exception.Assert;
import com.mingdao.edge.plugin.api.gateway.GatewayDownHandler;
import com.mingdao.edge.plugin.api.gateway.constants.GatewayConstants;
import com.mingdao.edge.plugin.api.gateway.dto.CommandDto;
import com.mingdao.edge.plugin.api.mapper.service.DataBaseService;
import com.mingdao.edge.plugin.data.sync.base.dto.DataDeleteCmdDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@Service
@SofaService(uniqueId = GatewayConstants.GATEWAY_CMD_BEAN_PREFIX + "data_delete")
public class DataDeleteHandler implements GatewayDownHandler {

    @SofaReference
    private DataBaseService dataBaseService;

    @Override
    public Object process(String messageId, CommandDto<?> commandDto) {
        try {
            Assert.notNull(commandDto, "参数不能为空");

            Object data = commandDto.getData();
            DataDeleteCmdDto dataDeleteCmdDto = null;
            if (data instanceof JSONObject) {
                JSONObject jsonObject = (JSONObject) commandDto.getData();
                dataDeleteCmdDto = jsonObject.toJavaObject(DataDeleteCmdDto.class);
            }

            Assert.notNull(dataDeleteCmdDto, "参数不能为空 {}", commandDto.getCommand());
            Assert.notEmpty(dataDeleteCmdDto.getIds(), "参数不能为空 {}", commandDto.getCommand());

            Assert.notNull(dataDeleteCmdDto, "查询参数不能为空");
            Assert.notNull(dataDeleteCmdDto.getDtName(), "查询参数不能为空");
            Assert.notNull(dataDeleteCmdDto.getSysOrg(), "查询参数不能为空");

            String tableName = "tb_" + dataDeleteCmdDto.getDtName();
            int rows = dataBaseService.deleteByIds(tableName, dataDeleteCmdDto.getIds());
            log.info("删除数据成功 {} {}", tableName, dataDeleteCmdDto.getIds());

        } catch (Exception e) {
            log.error(StrUtil.format("执行指令异常 {} {}", "data_delete", e.getMessage()), e);
        }
        return null;
    }
}

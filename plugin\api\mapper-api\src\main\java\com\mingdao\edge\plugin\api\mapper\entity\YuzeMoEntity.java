package com.mingdao.edge.plugin.api.mapper.entity;


import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @desc desc
 */
@Data
@TableName("tb_yz_mo")
@Entity(name = "tb_yz_mo")
public class YuzeMoEntity implements Serializable {

    @Id
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 批次号
     */
    private String batch;

    /**
     * 编码
     */
    private String code;

    /**
     * 部门编码
     */
    private String departmentcode;

    /**
     * 部门名称
     */
    private String departmentname;

    /**
     * 预计完工日期
     */
    private DateTime duedate;

    /**
     * 库位编码
     */
    private String entrepotpositioncode;

    /**
     * 库位名称
     */
    private String entrepotpositionname;

    /**
     * ERP工单编号
     */
    private String erpMocode;

    /**
     * ERP工单明细ID
     */
    private String erpModid;

    /**
     * ERP工单ID
     */
    private String erpMoid;

    /**
     * ERP工单序号
     */
    private String erpMoseq;

    /**
     * ERP销售订单编号
     */
    private String erpSocode;

    /**
     * ERP销售订单明细ID
     */
    private String erpSodid;

    /**
     * ERP销售订单ID
     */
    private String erpSoid;

    /**
     * ERP销售订单序号
     */
    private String erpSoseq;

    /**
     * 工单类型编码
     */
    private String motypecode;

    /**
     * 工单类型名称
     */
    private String motypename;

    /**
     * 开工日期
     */
    private DateTime opendate;

    /**
     * 物料编码
     */
    private String partcode;

    /**
     * 物料名称
     */
    private String partname;

    /**
     * 基本数量
     */
    private BigDecimal qtyforbase;

    /**
     * 生产请求数量
     */
    private BigDecimal qtyforproductrequest;

    /**
     * 请求数量
     */
    private BigDecimal qtyforrequest;

    /**
     * 不良品仓库编码
     */
    private String rejectswarehousecode;

    /**
     * 不良品仓库名称
     */
    private String rejectswarehousename;

    /**
     * 备注
     */
    private String remark;

    /**
     * 工艺路线主编码
     */
    private String routingmaincode;

    /**
     * 报废仓库编码
     */
    private String scrapwarehousecode;

    /**
     * 报废仓库名称
     */
    private String scrapwarehousename;

    /**
     * 更新时间
     */
    private DateTime timeUpdate;

    /**
     * 追溯码
     */
    private String tracecode;

    /**
     * 仓库编码
     */
    private String warehousecode;

    /**
     * 仓库名称
     */
    private String warehousename;
}

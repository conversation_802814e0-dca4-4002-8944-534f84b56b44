# DataTransmission 项目说明

## 项目简介

本项目是一个基于蚂蚁开源项目SOFAArk构建的数据同步程序，支持多插件架构，集成日志推送、数据同步、远程控制等功能。参考资料：https://www.sofastack.tech/projects/sofa-boot/sofa-ark-readme/

## 项目结构

```
DataTransmission
├── bom                  # 依赖管理
├── core                 # 核心模块（事件、API、通用工具等）
│   ├── ark-event        # Ark事件模块（共享包）
│   ├── biz-api          # 业务API模块（共享包）
│   ├── common           # 通用工具（共享包）
│   ├── common-biz-context   # 业务上下文（独立引用）
│   ├── common-biz-event     # 业务事件（共享包）
│   ├── common-cache         # 缓存模块（独立引用）
│   ├── common-disruptor     # 高性能队列（共享包）
│   ├── common-http          # HTTP工具（独立引用）
│   ├── common-mqtt          # MQTT工具（独立引用）
│   ├── common-service       # 公开服务调用（共享包）
├── plugin               # 插件目录（gateway、data-sync等）
│   ├── api              # 插件API聚合
│   │   ├── cloud-api
│   │   ├── data-exchanger-api
│   │   ├── data-sync-api
│   │   ├── gateway-api
│   │   ├── mapper-api
│   ├── cloud            # 云相关插件
│   ├── data-exchanger   # 数据交换插件
│   ├── data-source-driver   # 数据源驱动插件
│   ├── data-sync        # 数据同步插件
│   │   ├── data-sync-base
│   │   ├── data-sync-service
│   │   ├── data-sync-yuze
│   │   └── pom.xml
│   ├── gateway         # 网关插件，主业务代码
│   ├── third-part-api  # 第三方API插件
│   │   └── kingdee-bos-api    # 金蝶BOS API
```

### 子项目模块一览

| 模块名称                | 模块描述                     | 备注           |
|------------------------|------------------------------|----------------|
| bom                    | 依赖管理                     | Maven BOM      |
| core/ark-event         | Ark事件模块（共享包）        | 公共事件定义   |
| core/biz-api           | 基础biz API模块（共享包）        | 一般是大多数模块都会使用的才放这里 |
| core/common            | 通用工具（共享包）           |          |
| core/common-biz-context| 业务上下文（独立引用）       | 需要使用Spring获取Bean功能的，请使用这个包里的类。因为类隔离机制问题 |
| core/common-biz-event  | 业务事件（共享包）           | 公共事件定义，暂时无用 |
| core/common-cache      | 缓存模块（独立引用）         | 缓存实现       |
| core/common-disruptor  | 高性能队列（共享包）         | Disruptor队列  |
| core/common-http       | HTTP工具（独立引用）         | 基于OKHTTP3 |
| core/common-mqtt       | MQTT工具（独立引用）         | MQTTv5     |
| core/common-service    | 公开服务调用（共享包）       | 用于编程方式跨biz调用 |
| plugin/api             | 所有插件的API               | biz+biz api的方式提供服务 |
| plugin/cloud           | 云相关插件                   | 明道云相关接口    |
| plugin/data-exchanger  | 数据交换插件                 | （未使用）  |
| plugin/data-source-driver | 数据源驱动插件           | 本地可访问的数据源驱动 |
| plugin/data-sync/data-sync-base | 数据同步基础模块   | 数据同步相关的共享类 |
| plugin/data-sync/data-sync-service | 数据同步服务模块 | 数据同步服务   |
| plugin/data-sync/data-sync-yuze | 钰泽业务实现 | 业务实现     |
| plugin/gateway         | 主程序         | ark基座 |
| plugin/third-part-api  | 第三方API插件                | 金蝶等接口放在此处，都以biz来提供服务 |

### 模块分类

![](组件分类.png)

### 业务流程图

![](业务流程图.png)




## 关于SOFAArk的应用

- 分为基座、plugin和biz，本项目仅使用基座（gateway）、biz
- biz是一个业务包，是一个独立的springboot项目，但有一些不同
  - 部分公共的依赖包，是直接使用基座的，因此业务包很小，启动时也非常快
  - 基座与biz是不同的classloader，当使用基座的依赖包时，需注意静态变量的问题
- 使用的是动态合并部署，即是只启动基座，由基座请求云端获取biz再启动


## 启动流程

![](启动流程.jpg)



## 开发说明

- IDEA中配置启动参数：

    ```
    sofa.ark.embed.enable=true
    ```

    ```
    com.alipay.sofa.ark.master.biz=gateway
    ```

- 调试前，需重新打包一次（因动态部署的缘故）。
    - 全局打包：
    
      ```bash
      mvn -T 6 clean package -Dmodule.install.skip=false -Dmodule.deploy.skip=false
      ```
    
    - 仅打包data-sync组件：
    
      ```bash
      mvn -T 6 clean package -pl plugin/data-sync/data-sync-base,plugin/data-sync/data-sync-service,plugin/data-sync/data-sync-yuze -am -Dmodule.install.skip=false -Dmodule.deploy.skip=false
      ```
  
- 数据同步相关业务路径：plugin/data-sync目录下

- 如何新增一个同步业务？

    1. 在plugin/data-sync目录下，拷贝已有的实现包，更改包名
    2. 按业务引入对应的data-source-driver，当前支持http及mssql
    3. 在yml中配置相关的业务参数
    4. 分别创建上行及下行的抽象类，并继承对应的基础抽象类：AbstractDataSyncUp、AbstractDataSyncDown
    5. 创建业务处理类，并继承相应的上行或下行抽象类
    6. 在web端增加模块、mqtt信息后，即可打包并启动调试。

- 如何同时运行不同版本的模块？

    1. 修改以下代码：
    
       ```xml
       <!--在pom中修改：-->
       <webContextPath>${artifactId}/${revision}</webContextPath>
       ```
       ```yaml
       #在application.yml中加入：
       app.version: ${project.version}
       ```
       那么web接口访问路径则为：http://127.0.0.1:1995/data-source-driver-mssql/1.0.0/...
       
    2. 


## 部署说明

### 环境依赖

| 类别     | 名称   | 内容                   |
|----------|--------|------------------------|
| 服务器   | CPU    | 4核心以上              |
| 服务器   | 内存   | 8GB以上                |
| 服务器   | 硬盘   | 80GB以上               |
| 操作系统 | Windows或Linux | Windows版本10以上，Linux版本建议使用CentOS 7+/Ubuntu 18.04+ |
| 网络 | 上行带宽 | 1Mbps以上 |
| 网络 | 下行带宽 | 10Mbps以上 |

### 部署步骤

1. 安装java8
2. 拷贝以下文件到部署目录中
    - gateway-1.0.0.jar
    - 修改启动脚本中的用户变量“md_init_user”、“md_init_password”
    - 启动脚本：`start.bat` 或 `server.sh`
3. 启动服务：
   - Windows: `start.bat`
   - Linux: `server.sh`

## 使用说明

- 系统日志查看：
    - 本地日志查看：在部署目录的logs/edge-gateway目录下。
    - 通过接口查看：http://server/data-transmission-service/edge/cmd/log/fetch?sessionId=1
- 系统数据查看：
    - 本地数据查看：
        ```
        http://127.0.0.1:1995/md-edge/login.do

        JDBC URL: jdbc:h2:file:./database/md-edge;IGNORECASE=TRUE;AUTO_SERVER=TRUE;DB_CLOSE_ON_EXIT=FALSE;MODE=LEGACY;

        User Name: md-edge
        Password: md@edge

        ```
    - 远程数据查看：
        进入菜单：实时同步任务-选择任务-打开“查看数据日志”

- 模块升级：
    - 本地升级：拷贝biz到部署目录download上，然后到“模块配置”中操作重启
    - 远程升级：升级版本号后，到“模块配置”中操作重启

- 数据重传：
    - 本地操作：停止应用服务后，删除cache、database目录并重启，也可进入数据库修改对应数据的状态与创建时间即可
    - 远程操作：通过数据日志界面的“重传”或“优先上传”即可

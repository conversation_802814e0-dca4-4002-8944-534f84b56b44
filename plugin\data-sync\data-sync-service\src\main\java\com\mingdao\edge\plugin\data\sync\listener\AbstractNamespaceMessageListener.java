package com.mingdao.edge.plugin.data.sync.listener;

import cn.hutool.core.lang.id.NanoId;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.mingdao.edge.core.common.context.SpringContextUtils;
import com.mingdao.edge.core.pulsar.utils.PulsarClientUtil;
import com.mingdao.edge.plugin.api.gateway.dto.DownMessage;
import com.mingdao.edge.plugin.data.sync.context.TaskContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.*;

import java.nio.charset.StandardCharsets;

/**
 * 抽象Pulsar消息监听器
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Slf4j
public abstract class AbstractNamespaceMessageListener implements MessageListener<byte[]> {

    protected final TaskContextHolder taskContextHolder;

    public AbstractNamespaceMessageListener(String tenantName, String namespace, String topic) {
        this.taskContextHolder = SpringContextUtils.getBean(TaskContextHolder.class);
        try {
            log.info("初始化订阅：{}-{}-{}", tenantName, namespace, topic);
            String subscriptionName = StrUtil.format("{}-{}-{}", tenantName, namespace, NanoId.randomNanoId(4));
            PulsarClientUtil.subscribeNamespaceAllTopic(tenantName, namespace, subscriptionName, SubscriptionType.Shared, this);
        } catch (PulsarClientException e) {
            String errorMsg = StrUtil.format("{}-{}-{}：{}", tenantName, namespace, topic, e.getMessage());
            log.error("创建订阅失败: " + errorMsg);
        }
    }


    @Override
    public void received(Consumer<byte[]> consumer, Message<byte[]> message) {
        try {
            // 获取消息内容
            String messageContent = new String(message.getData(), StandardCharsets.UTF_8);

            //// 获取消息属性
            //String messageId = message.getMessageId().toString();
            //String topic = message.getTopicName();
            //String key = message.getKey();
            //long publishTime = message.getPublishTime();

            //log.info("接收到Pulsar消息 {} - 主题: {}, 消息ID: {}, Key: {}, 发布时间: {}, 内容: {}",
            //        this.getClass().getSimpleName(),
            //        topic, messageId, key, publishTime, messageContent);


            DownMessage<?> downMessage = JSON.parseObject(messageContent, DownMessage.class);
            if (downMessage == null) {
                log.warn("[网关下行消息]异常：缺少实体内容");
                return;
            }

            process(downMessage);

        } catch (Exception e) {
            log.error("[网关下行消息]异常: " + e.getMessage(), e);
        } finally {
            try {
                consumer.acknowledge(message);
            } catch (PulsarClientException e) {
                log.error("发送ack失败" + e.getMessage());
            }
        }
    }

    protected abstract void process(DownMessage<?> downMessage);
}

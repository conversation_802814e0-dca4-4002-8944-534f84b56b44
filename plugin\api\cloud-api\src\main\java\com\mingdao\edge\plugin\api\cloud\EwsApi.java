package com.mingdao.edge.plugin.api.cloud;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.core.common.dto.BaseResponse;

import java.util.Map;

public interface EwsApi {
    BaseResponse<JSONArray> getTaskList();
    BaseResponse<JSONObject> getElementList(String module, String page);
    /**
     * <p>
     * 获取满足条件的入库方案列表数据
     * </p>
     * @Author: DLg
     * @since: 2024/2/2 8:51
     * @return net.sf.json.JSONArray
     **/
    BaseResponse<JSONArray> getWarehouseProjectList(String dtBasePage, String data_task, Integer transMitStatus, Integer status);
    BaseResponse<JSONArray> getColumns(String dtBasePage);
    BaseResponse<JSONObject> updateSuppliesRecordStatus(Map<String, Object> updateObj);
    BaseResponse<JSONArray> searchWarehouseBaseList(JSONObject params);
    BaseResponse<JSONObject> updateWarehouseDownloadStatus(JSONObject updateObj);
    BaseResponse<JSONArray> searchSuppliesDetailList(JSONObject warehouseproject);
    BaseResponse<JSONObject> updateHistoryStatus(JSONObject params);
}

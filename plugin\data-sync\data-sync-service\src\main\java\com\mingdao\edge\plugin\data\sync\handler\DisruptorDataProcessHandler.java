package com.mingdao.edge.plugin.data.sync.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.core.common.biz.event.IBizEventHandler;
import com.mingdao.edge.plugin.data.sync.base.constants.EventTypeConstants;
import com.mingdao.edge.plugin.api.data.sync.service.DataSyncService;
import com.mingdao.edge.plugin.api.gateway.dto.DataCollectEvent;
import com.mingdao.edge.plugin.data.sync.context.TaskContext;
import com.mingdao.edge.plugin.data.sync.context.TaskContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 临时使用Biz内部Event，拆分时应换为外部Event
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@Component(EventTypeConstants.DISRUPTOR_DATA_PROCESS)
public class DisruptorDataProcessHandler implements IBizEventHandler<DataCollectEvent> {

    @Resource
    private TaskContextHolder taskContextHolder;

    @Override
    public void process(DataCollectEvent eventDTO) {
        Long id = (Long) eventDTO.getData();
        TaskContext taskContext = taskContextHolder.getTaskContext(eventDTO.getDtName());
        if (taskContext == null) {
            log.warn("未找到任务上下文 {}", eventDTO.getDtName());
            return;
        }

        DataSyncService dataSyncService = taskContext.getDataSyncService();
        if (dataSyncService == null) {
            log.warn("未找到任务处理器 {}", eventDTO.getDtName());
            return;
        }
        try {
            JSONObject parsedData = dataSyncService.getPraseData(id);
            if (parsedData != null) {
                dataSyncService.transferData(eventDTO.toEventMessage(parsedData));
                log.info("转换数据成功 {}", parsedData);
            }
        } catch (Exception e) {
            log.error(StrUtil.format(" Disruptor 数据处理失败 {}: {}", eventDTO.getDtName(), e.getMessage()), e);
        }
    }
}

package com.mingdao.edge.plugin.data.sync.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.mingdao.edge.plugin.api.data.sync.service.DataSyncService;
import com.mingdao.edge.plugin.data.sync.base.constants.DataSyncConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 生产订单
 * <AUTHOR>
 * @since 2025-06-13
 */
@Slf4j
@Service
@SofaService(uniqueId = DataSyncConstants.DATA_SYNC_SERVICE + "_yuze_partupload")
public class YuzePartDataSyncUpImpl extends AbstractYuzeDataSyncUp implements DataSyncService {

    public YuzePartDataSyncUpImpl() {
        super("yuze", "partupload", 1000);
    }

    @Override
    public void init(JSONObject taskInfo) {
        super.init(taskInfo);
    }

    @Override
    public boolean convertSpecField(String fieldName, Object value, Map<String, Object> result) {
        boolean isModify = false;
        switch (fieldName) {
            case "BESELF":
            case "PURCHASE":
            case "PROXYFOREIGN":
            case "AVAILABLE":
                isModify = true;
                Integer v = "是".equals((String) value) ? 1 : 0;
                result.put(fieldName, v);
                break;
            default:
                break;
        }
        return isModify;
    }
}

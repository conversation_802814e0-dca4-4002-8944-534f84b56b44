package com.mingdao.edge.plugin.data.sync.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.core.common.exception.Assert;
import com.mingdao.edge.plugin.api.data.sync.service.DataSyncService;
import com.mingdao.edge.plugin.api.gateway.constants.PulsarNamespace;
import com.mingdao.edge.plugin.api.gateway.dto.DataDto;
import com.mingdao.edge.plugin.api.gateway.dto.DownMessage;
import com.mingdao.edge.plugin.api.gateway.enums.MessageType;
import com.mingdao.edge.plugin.data.sync.context.TaskContext;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedHashMap;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Slf4j
public class DataDownMessageListener extends AbstractNamespaceMessageListener {

    public DataDownMessageListener(String tenantName) {
        super(tenantName, PulsarNamespace.DATA, ".*");
    }

    @Override
    protected void process(DownMessage<?> downMessage) {
        Object downMessageData = downMessage.getData();
        JSONObject downMessageDataJson = null;
        if (downMessageData instanceof JSONObject) {
            downMessageDataJson = (JSONObject) downMessageData;
        } else if (downMessageData instanceof LinkedHashMap) {
            downMessageDataJson = new JSONObject((LinkedHashMap) downMessageData);
        }

        MessageType downType = downMessage.getDownType();

        if (MessageType.DATA_UP_CALLBACK.equals(downType)) {
            DataDto<?> dataDto = downMessageDataJson.toJavaObject(DataDto.class);
            Assert.notNull(dataDto, "数据实体转换失败");

            String dtName = dataDto.getDataTypeName();

            TaskContext taskContext = taskContextHolder.getTaskContext(dtName);

            DataSyncService dataSyncService = taskContext.getDataSyncService();
            Assert.notNull(dataSyncService, "缺少消息处理器");
            if (downMessage.getDownType() == MessageType.DATA_UP_CALLBACK) {
                log.info("消费到数据状态回调 {}", JSON.toJSONString(downMessage));
                dataSyncService.transferDataCallback(downMessage);
            }
        }
    }
}

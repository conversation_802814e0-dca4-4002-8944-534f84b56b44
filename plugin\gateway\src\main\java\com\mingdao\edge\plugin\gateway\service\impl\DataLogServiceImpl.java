package com.mingdao.edge.plugin.gateway.service.impl;

import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.mingdao.edge.plugin.api.mapper.DataLogMapper;
import com.mingdao.edge.plugin.api.mapper.DataLogMsgMapper;
import com.mingdao.edge.plugin.api.mapper.entity.DataLog;
import com.mingdao.edge.plugin.api.mapper.entity.DataLogMsg;
import com.mingdao.edge.plugin.api.mapper.service.DataLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Slf4j
@Service
@SofaService
@RequiredArgsConstructor
public class DataLogServiceImpl implements DataLogService {

    private final DataLogMapper dataLogMapper;
    private final DataLogMsgMapper dataLogMsgMapper;

    @Async
    @Transactional(rollbackFor = Exception.class)
    public void addDataLog(String sysOrg, String bizType, String bizId, Long dataId, String MD_STATE, String MD_DT_NAME) {
        DataLog dataLog = new DataLog();
        dataLog.setBizType(bizType);
        dataLog.setBizId(bizId);
        dataLog.setDataId(dataId);
        dataLog.setMD_STATE(MD_STATE);
        dataLog.setMD_DT_NAME(MD_DT_NAME);
        dataLog.setSysOrg(sysOrg);
        dataLog.setCreateTime(System.currentTimeMillis());
        dataLog.setUpdateTime(System.currentTimeMillis());

        log.debug("写入日志 {}", dataId);
        dataLogMapper.insert(dataLog);
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    public void addDataLogMsg(Long dataId, String bizType, String bizId, String msg) {

        DataLog dataLog = dataLogMapper.selectByDataId(dataId, bizType, bizId);
        if (dataLog != null) {
            DataLogMsg dataLogMsg = new DataLogMsg();
            dataLogMsg.setLogId(dataLog.getId());
            dataLogMsg.setMsg(msg);
            dataLogMsg.setCreateTime(System.currentTimeMillis());
            dataLogMsgMapper.insert(dataLogMsg);
        }

    }
}

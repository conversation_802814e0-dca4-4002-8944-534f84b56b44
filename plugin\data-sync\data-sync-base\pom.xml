<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.mingdao.edge.plugin</groupId>
        <artifactId>data-sync</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>data-sync-base</artifactId>
    <version>${data-sync-base.version}</version>
    <name>${artifactId}</name>
    <packaging>jar</packaging>

    <properties>
        <cloud-api.version>1.0.0</cloud-api.version>
    </properties>

    <dependencies>
        <!-- project -->
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common-biz-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.plugin</groupId>
            <artifactId>data-sync-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.plugin</groupId>
            <artifactId>data-source-driver-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common-service</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common-pulsar</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.plugin</groupId>
            <artifactId>cloud-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.plugin</groupId>
            <artifactId>gateway-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.plugin</groupId>
            <artifactId>mapper-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common-biz-event</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>biz-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- sofa-ark-plugin -->
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofa-ark-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- end sofa-ark-plugin -->
        <dependency>
            <groupId>org.apache.pulsar</groupId>
            <artifactId>pulsar-client</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>

<?xml version="1.0" encoding="UTF-8"?>
<included>

	<include resource="default.xml"/>
    <appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <file>${LOG_HOME}/error.log</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!--滚动策略-->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--路径-->
            <fileNamePattern>${LOG_HOME}/%d{yyyy-MM,aux}/%d{yyyy-MM-dd}-error-%i.log.gz</fileNamePattern>
            <!-- 清除m天前的日志 -->
            <maxHistory>30</maxHistory>
            <!-- 单文件超过后归档压缩 -->
            <maxFileSize>1GB</maxFileSize>
            <!-- 总大小 -->
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>
</included>

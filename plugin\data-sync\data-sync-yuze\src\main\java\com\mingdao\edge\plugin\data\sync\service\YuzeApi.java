package com.mingdao.edge.plugin.data.sync.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alipay.sofa.runtime.api.annotation.SofaReference;
import com.mingdao.edge.plugin.data.source.driver.api.dto.ApiPageQueryDto;
import com.mingdao.edge.plugin.data.source.driver.api.exception.ApiException;
import com.mingdao.edge.plugin.data.source.driver.api.service.DataCollector;
import com.mingdao.edge.plugin.data.sync.property.YuzeProperty;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Component
public class YuzeApi {

    @Resource
    protected YuzeProperty yuzeProperty;
    @SofaReference
    protected DataCollector dataCollector;

    public JSONObject getUserDataFromApi(String url, Long dtoUptime, Integer page, Integer pageSize) throws ApiException {
        if (dtoUptime != null && dtoUptime > 0) {
            String dto_uptime2 = DateUtil.format(new Date(dtoUptime), "yyyy-MM-dd HH:mm:ss");
            String example = "{\"timeUpdate\": [\""+dto_uptime2+"\",\"9999-01-01 00:00:00\"]}";

            JSONObject queryParams = new JSONObject();
            queryParams.put("query", JSONObject.parseObject(example));
            queryParams.put("pageNum", page);
            queryParams.put("pageSize", pageSize);

            return requestApi("/" + url.split("#")[1], HttpMethod.POST, new JSONObject(), queryParams, page, pageSize);
        }
        return null;
    }

    public JSONObject requestApi(String url, HttpMethod httpMethod, JSONObject header, JSONObject queryParams, Integer page, Integer pageSize) throws ApiException {
        long timestamp = System.currentTimeMillis() / 1000;
        String nonce = UUID.fastUUID().toString();

        String signature = yuzeProperty.getSignature(timestamp, nonce);
        header.put("appid", yuzeProperty.getAppId());
        header.put("timestamp", String.valueOf(timestamp));
        header.put("nonce", nonce);
        header.put("signature", signature);

        ApiPageQueryDto apiPageQueryDto = new ApiPageQueryDto();
        apiPageQueryDto.setUrl(yuzeProperty.getUrl() + url);
        apiPageQueryDto.setPage(page);
        apiPageQueryDto.setSize(pageSize);
        apiPageQueryDto.setHeader(header);
        apiPageQueryDto.setParams(queryParams);
        apiPageQueryDto.setMethod(httpMethod);
        return dataCollector.getPageData(apiPageQueryDto);
    }



    public JSONObject getMmMes(String autoId, String moCode) throws ApiException {
        String urlPath = StrUtil.format("/api/mmes/{}/{}", autoId, moCode);
        return requestApi(urlPath, HttpMethod.GET, new JSONObject(), null, null, null);
    }

    public JSONObject saveMmMes(JSONObject params) throws ApiException {
        String urlPath = "/api/mmes";
        return requestApi(urlPath, HttpMethod.POST, new JSONObject(), params, null, null);
    }

    public JSONObject editMmMes(JSONObject params) throws ApiException {
        String urlPath = "/api/mmes";
        return requestApi(urlPath, HttpMethod.PUT, new JSONObject(), params, null, null);
    }


    public JSONObject getMlMes(String erpId, String moCode) throws ApiException {
        String urlPath = StrUtil.format("/api/mlmes/{}/{}", erpId, moCode);
        return requestApi(urlPath, HttpMethod.GET, new JSONObject(), null, null, null);
    }

    public JSONObject saveMlMes(JSONObject params) throws ApiException {
        String urlPath = "/api/mlmes";
        return requestApi(urlPath, HttpMethod.POST, new JSONObject(), params, null, null);
    }

    public JSONObject editMlMes(JSONObject params) throws ApiException {
        String urlPath = "/api/mlmes";
        return requestApi(urlPath, HttpMethod.PUT, new JSONObject(), params, null, null);
    }
}

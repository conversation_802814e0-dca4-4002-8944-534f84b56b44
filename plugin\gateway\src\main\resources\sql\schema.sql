create table if not exists tb_data_log
(
    id                               bigint auto_increment primary key,
    sys_org                          varchar(16)   not null,
    biz_type                         varchar(50)   not null,
    biz_id                           varchar(128)  not null,
    data_id                          bigint        not null,
    MD_STATE                         varchar(8)    not null,
    MD_DT_NAME                       varchar(32)   not null,
    create_time                      bigint        not null default now(),
    update_time                      bigint        not null
);

create table if not exists tb_data_log_msg
(
    id                               bigint auto_increment primary key,
    log_id                           bigint not null,
    msg                              TEXT,
    create_time                      bigint not null default now()
);

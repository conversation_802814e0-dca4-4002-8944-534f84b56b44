package com.mingdao.edge.core.pulsar.constants;

/**
 * Pulsar相关常量定义
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
public class PulsarConstants {
    
    /**
     * 默认的Pulsar服务地址
     */
    public static final String DEFAULT_SERVICE_URL = "pulsar://localhost:6650";
    
    /**
     * 默认的连接超时时间（秒）
     */
    public static final int DEFAULT_CONNECTION_TIMEOUT_SECONDS = 30;
    
    /**
     * 默认的操作超时时间（秒）
     */
    public static final int DEFAULT_OPERATION_TIMEOUT_SECONDS = 30;
    
    /**
     * 默认的接收队列大小
     */
    public static final int DEFAULT_RECEIVER_QUEUE_SIZE = 1000;
    
    /**
     * Spring Bean名称前缀
     */
    public static final String PULSAR_CLIENT_BEAN_NAME = "pulsarClient";
    public static final String PULSAR_PRODUCER_BEAN_PREFIX = "pulsarProducer_";
    public static final String PULSAR_CONSUMER_BEAN_PREFIX = "pulsarConsumer_";
    
    /**
     * 主题名称分隔符
     */
    public static final String TOPIC_SEPARATOR = "/";
    public static final String TOPIC_REPLACEMENT = "_";
    
    /**
     * 常用主题前缀
     */
    public static final String EDGE_TOPIC_PREFIX = "edge/";
    public static final String DATA_TOPIC_PREFIX = "edge/data/";
    public static final String CONTROL_TOPIC_PREFIX = "edge/control/";
    public static final String STATUS_TOPIC_PREFIX = "edge/status/";
    
    /**
     * 订阅名称后缀
     */
    public static final String SUBSCRIPTION_SUFFIX = "_subscription";
    
    /**
     * 消息属性键
     */
    public static final String MESSAGE_TIMESTAMP_KEY = "timestamp";
    public static final String MESSAGE_SOURCE_KEY = "source";
    public static final String MESSAGE_TYPE_KEY = "type";
    public static final String MESSAGE_VERSION_KEY = "version";
}

@echo off
setlocal

set "workpath=D:\application\edge"
set JAVA_EXE=%JAVA_HOME%\bin\java.exe

cd /d %workpath%

set md_api_path=https://transfer.mingdao-info.com
set md_domain=https://transfer.mingdao-info.com/data-transmission-service
set md_ews3_path=https://transfer.mingdao-info.com/ews3/
set md_init_password=de26412b72acc1a969648a48d4d328e00ff1caf1d33be4dd8031b5f74bb02bc9
set md_init_path=https://transfer.mingdao-info.com/ewstransfer/
set md_init_user=9e917ea057594346b05104752a59dee9
set md_sso_url=https://transfer.mingdao-info.com/oauth
set md_tasksinitpath=https://transfer.mingdao-info.com/ewstransfer


"%JAVA_EXE%" -jar  -Dfile.encoding=UTF-8 -Dsofa.ark.embed.enable=true -Dcom.alipay.sofa.ark.master.biz=edge-gateway -jar gateway-1.0.0.jar --server.port=19950

pause

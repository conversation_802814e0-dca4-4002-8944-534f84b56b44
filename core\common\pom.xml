<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.mingdao.edge</groupId>
        <artifactId>core</artifactId>
        <version>${revision}</version>
    </parent>

    <groupId>com.mingdao.edge.core</groupId>
    <artifactId>common</artifactId>
    <version>${common.version}</version>
    <packaging>jar</packaging>

    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common-biz-context</artifactId>
            <scope>provided</scope>
        </dependency>

    </dependencies>

</project>

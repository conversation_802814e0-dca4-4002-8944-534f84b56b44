package com.mingdao.edge.plugin.gateway.service.impl;

import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.alipay.sofa.runtime.api.annotation.SofaServiceBinding;
import com.mingdao.edge.core.common.cache.EdgeCacheManager;
import com.mingdao.edge.core.common.dto.BaseResponse;
import com.mingdao.edge.core.pulsar.utils.PulsarClientUtil;
import com.mingdao.edge.plugin.api.gateway.EdgeStateService;
import com.mingdao.edge.plugin.api.gateway.constants.PulsarNamespace;
import com.mingdao.edge.plugin.api.gateway.dto.EdgeState;
import com.mingdao.edge.plugin.api.gateway.dto.UpMessage;
import com.mingdao.edge.plugin.api.gateway.enums.MessageType;
import com.mingdao.edge.plugin.api.gateway.enums.StateEnum;
import com.mingdao.edge.plugin.gateway.constants.BizConstant;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
@SofaService(bindings = {@SofaServiceBinding(serialize = false)})
public class EdgeStateServiceImpl implements EdgeStateService {

    @Resource
    private EdgeCacheManager edgeCacheManager;

    @Override
    public void putState(EdgeState edgeState) {
        edgeCacheManager.hSet(BizConstant.CACHE_KEY_STATE, edgeState.getCacheKey(), edgeState);

        UpMessage<BaseResponse<EdgeState>> upMessage = new UpMessage<>(MessageType.STATE_UP, BaseResponse.ok(edgeState));
        String topic = PulsarClientUtil.getFullTopicByOrgCode(edgeState.getSysOrg(), PulsarNamespace.STATE_UP, "edge-state");
        PulsarClientUtil.sendAsync(topic, upMessage);
    }

    @Override
    public void putState(StateEnum stateEnum, String bizName, String value, Date time, Boolean editable, Long sysOrg) {
        EdgeState edgeState = new EdgeState(stateEnum.getStateName(), stateEnum.getStateKey(), bizName, value, time, editable, sysOrg);
        putState(edgeState);
    }

    @Override
    public List<Object> getAllStates() {
        return edgeCacheManager.hGetAllValue(BizConstant.CACHE_KEY_STATE);
    }
}

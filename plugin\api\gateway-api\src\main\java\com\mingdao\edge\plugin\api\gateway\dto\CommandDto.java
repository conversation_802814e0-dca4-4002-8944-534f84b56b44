package com.mingdao.edge.plugin.api.gateway.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
public class CommandDto<T> implements Serializable {
    private final static long serialVersionUID = 1L;

    private String command;
    private String replyTopic;
    private T data;

    public JSONObject toJSONObject() {
        return (JSONObject) this.data;
    }

}

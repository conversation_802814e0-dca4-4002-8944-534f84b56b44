package com.mingdao.edge.plugin.api.data.sync.dto;

import com.mingdao.edge.plugin.api.gateway.dto.QueryModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryEdgeDataDto extends QueryModel {

    private Long sysOrg;
    private String dtName;
    private Map<String, Object> params;
}

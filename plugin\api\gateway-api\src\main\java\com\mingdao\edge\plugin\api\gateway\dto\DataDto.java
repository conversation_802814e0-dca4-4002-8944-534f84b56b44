package com.mingdao.edge.plugin.api.gateway.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
public class DataDto<T> implements Serializable {
    private final static long serialVersionUID = 1L;

    private String id;
    /**
     * dtName
     */
    private String dataTypeName;
    private String replyTopic;
    private T data;

}

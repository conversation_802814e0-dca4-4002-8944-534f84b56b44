package com.mingdao.edge.plugin.gateway;

import com.alipay.sofa.ark.api.ArkConfigs;
import com.alipay.sofa.ark.spi.constant.Constants;
import com.mingdao.edge.core.common.constants.EdgeConstant;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableScheduling;


@EnableScheduling
@ImportResource({"classpath*:META-INF/spring/service.xml"})
@EntityScan({"com.mingdao.edge.core.mapper.entity"})
@MapperScan({"com.mingdao.edge.plugin.api.mapper"})
@SpringBootApplication(scanBasePackages = {"com.mingdao.edge"})
public class EdgeGatewayApplication {

    public static void main(String[] args) {
        // 配置sofa内部biz包下载位置
        ArkConfigs.setSystemProperty(Constants.CONFIG_INSTALL_BIZ_DIR, EdgeConstant.PLUGIN_PATH);
        SpringApplication.run(EdgeGatewayApplication.class, args);
    }

}

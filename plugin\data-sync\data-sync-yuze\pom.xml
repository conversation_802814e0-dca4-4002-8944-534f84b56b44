<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.mingdao.edge.plugin</groupId>
        <artifactId>data-sync</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>data-sync-yuze</artifactId>
    <name>${artifactId}</name>
    <version>${data-sync-yuze.version}</version>
    <packaging>jar</packaging>

    <properties>
    </properties>

    <dependencies>
        <!-- project -->
        <dependency>
            <groupId>com.mingdao.edge.plugin</groupId>
            <artifactId>data-sync-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.plugin</groupId>
            <artifactId>cloud-api</artifactId>
            <version>${cloud-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common-mqtt</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.plugin</groupId>
            <artifactId>gateway-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common-http</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.plugin</groupId>
            <artifactId>mapper-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common-biz-event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>biz-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- sofa-ark-plugin -->
        <!-- end sofa-ark-plugin -->
    </dependencies>

    <build>
        <plugins>
            <!--<plugin>-->
            <!--    <groupId>com.alipay.sofa</groupId>-->
            <!--    <artifactId>sofa-ark-plugin-maven-plugin</artifactId>-->
            <!--    <version>${sofa.ark.version}</version>-->
            <!--    <executions>-->
            <!--        <execution>-->
            <!--            <id>default-cli</id>-->
            <!--            <goals>-->
            <!--                <goal>ark-plugin</goal>-->
            <!--            </goals>-->
            <!--        </execution>-->
            <!--    </executions>-->
            <!--    <configuration>-->
            <!--        <attach>false</attach>-->
            <!--        <outputDirectory>${user.dir}/download</outputDirectory>-->
            <!--        <pluginName>${artifactId}</pluginName>-->
            <!--        &lt;!&ndash; 配置导入类、资源 &ndash;&gt;-->
            <!--        <imported>-->
            <!--            &lt;!&ndash; 配置需要优先从其他 ark plugin 加载的 package &ndash;&gt;-->
            <!--            <packages>-->
            <!--                <package>javax.servlet</package>-->
            <!--                <package>org.springframework.*</package>-->
            <!--            </packages>-->
            <!--            &lt;!&ndash; 配置需要优先从其他 ark plugin 加载的 class &ndash;&gt;-->
            <!--            &lt;!&ndash;<classes>&ndash;&gt;-->
            <!--            &lt;!&ndash;    <class>com.alipay.sofa.rpc.config.ProviderConfig</class>&ndash;&gt;-->
            <!--            &lt;!&ndash;</classes>&ndash;&gt;-->
            <!--            &lt;!&ndash; 配置需要优先从其他 ark plugin 加载的资源 &ndash;&gt;-->
            <!--            <resources>-->
            <!--                <resource>META-INF/spring/bean.xml</resource>>-->
            <!--            </resources>-->
            <!--        </imported>-->
            <!--    </configuration>-->
            <!--</plugin>-->
            <plugin>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-ark-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-cli</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <skipArkExecutable>true</skipArkExecutable>
                    <outputDirectory>${user.dir}/download</outputDirectory>
                    <bizName>${artifactId}</bizName>
                    <webContextPath>${artifactId}</webContextPath>
                    <declaredMode>true</declaredMode>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>

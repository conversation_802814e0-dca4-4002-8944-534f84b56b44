<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.mingdao</groupId>
    <artifactId>edge</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>Edge Gateway</name>
    <description>边缘网关</description>

    <modules>
        <!-- Project Management -->
        <module>bom</module>
        <!-- 0 : Core + API + SPI -->
        <module>core</module>
        <!-- 1 : Plugin Implements. -->
        <module>plugin</module>
    </modules>

    <properties>
        <!-- Build args -->
        <revision>1.0.0</revision>
        <module.install.skip>false</module.install.skip>
        <module.deploy.skip>false</module.deploy.skip>
        <maven.javadoc.skip>true</maven.javadoc.skip>
        <module.jacoco.skip>true</module.jacoco.skip>
        <skipTests>true</skipTests>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>utf-8</project.build.sourceEncoding>
        <slf4j.version>1.7.21</slf4j.version>
        <spring.boot.version>2.7.0</spring.boot.version>
        <sofa.ark.version>2.3.1</sofa.ark.version>

        <!--父包版本-->
        <core.version>1.0.0</core.version>
        <api.version>1.0.0</api.version>
        <data-source-driver.version>1.0.0</data-source-driver.version>
        <data-sync.version>1.0.0</data-sync.version>

        <!-- Project artifact versions -->
        <common-biz-context.version>1.0.0</common-biz-context.version>
        <common-service.version>1.0.0</common-service.version>
        <common.version>1.0.0</common.version>
        <common-biz-event.version>1.0.0</common-biz-event.version>
        <biz-api.version>1.0.0</biz-api.version>
        <ark-event.version>1.0.0</ark-event.version>
        <common-cache.version>1.0.0</common-cache.version>
        <common-http.version>1.0.0</common-http.version>
        <common-mqtt.version>1.0.0</common-mqtt.version>
        <common-pulsar.version>1.0.0</common-pulsar.version>
        <mapper-api.version>1.0.0</mapper-api.version>
        <common-disruptor.version>1.0.0</common-disruptor.version>
        <cloud-api.version>1.0.0</cloud-api.version>
        <gateway-api.version>1.0.0</gateway-api.version>
        <data-source-driver-api.version>1.0.0</data-source-driver-api.version>
        <data-sync-api.version>1.0.0</data-sync-api.version>
        <data-sync-base.version>1.0.0</data-sync-base.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- project artifact -->
            <dependency>
                <groupId>com.mingdao.edge.core</groupId>
                <artifactId>common-biz-context</artifactId>
                <version>${common-biz-context.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mingdao.edge.core</groupId>
                <artifactId>common-service</artifactId>
                <version>${common-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mingdao.edge.core</groupId>
                <artifactId>common</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mingdao.edge.core</groupId>
                <artifactId>common-biz-event</artifactId>
                <version>${common-biz-event.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mingdao.edge.core</groupId>
                <artifactId>biz-api</artifactId>
                <version>${biz-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mingdao.edge.core</groupId>
                <artifactId>ark-event</artifactId>
                <version>${ark-event.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mingdao.edge.core</groupId>
                <artifactId>common-cache</artifactId>
                <version>${common-cache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mingdao.edge.core</groupId>
                <artifactId>common-http</artifactId>
                <version>${common-http.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mingdao.edge.plugin</groupId>
                <artifactId>mapper-api</artifactId>
                <version>${mapper-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mingdao.edge.core</groupId>
                <artifactId>common-disruptor</artifactId>
                <version>${common-disruptor.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mingdao.edge.plugin</groupId>
                <artifactId>cloud-api</artifactId>
                <version>${cloud-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mingdao.edge.plugin</groupId>
                <artifactId>gateway-api</artifactId>
                <version>${gateway-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mingdao.edge.plugin</groupId>
                <artifactId>data-source-driver-api</artifactId>
                <version>${data-source-driver-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mingdao.edge.plugin</groupId>
                <artifactId>data-sync-api</artifactId>
                <version>${data-sync-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mingdao.edge.plugin</groupId>
                <artifactId>data-sync-base</artifactId>
                <version>${data-sync-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mingdao.edge.core</groupId>
                <artifactId>common-mqtt</artifactId>
                <version>${common-mqtt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mingdao.edge.core</groupId>
                <artifactId>common-pulsar</artifactId>
                <version>${common-pulsar.version}</version>
            </dependency>
            <!-- end project artifact -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.mingdao.edge</groupId>
                <artifactId>bom</artifactId>
                <version>${revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.38</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>



    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>1.6.1</version>
            </extension>
        </extensions>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.alipay.sofa</groupId>
                    <artifactId>sofa-ark-maven-plugin</artifactId>
                    <version>${sofa.ark.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.0.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.10.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>3.1.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.22.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.12</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <configuration>
                    <filesets>
                        <fileset>
                            <directory>${project.basedir}/cache</directory>
                            <includes>
                                <include>**/*</include>
                            </includes>
                            <followSymlinks>false</followSymlinks>
                        </fileset>
                        <fileset>
                            <directory>${project.basedir}/database</directory>
                            <includes>
                                <include>**/*</include>
                            </includes>
                            <followSymlinks>false</followSymlinks>
                        </fileset>
                    </filesets>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>${module.install.skip}</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>${module.deploy.skip}</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <addMavenDescriptor>false</addMavenDescriptor>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <detectOfflineLinks>true</detectOfflineLinks>
                    <breakiterator>true</breakiterator>
                    <author>false</author>
                    <keywords>true</keywords>
                    <skip>${maven.javadoc.skip}</skip>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <configuration>
                    <skip>${module.jacoco.skip}</skip>
                </configuration>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report-aggregate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>jdk8</id>
            <activation>
                <jdk>[1.8,)</jdk>
            </activation>
            <properties>
                <maven.javadoc.failOnError>false</maven.javadoc.failOnError>
                <maven.javadoc.quiet>true</maven.javadoc.quiet>
            </properties>
        </profile>
        <profile>
            <id>ci-install</id>
            <properties>
                <skipTests>true</skipTests>
                <maven.javadoc.skip>true</maven.javadoc.skip>
                <module.jacoco.skip>true</module.jacoco.skip>
            </properties>
        </profile>
        <profile>
            <id>ci-test</id>
            <properties>
                <dependency.test.skip>false</dependency.test.skip>
                <maven.javadoc.skip>true</maven.javadoc.skip>
                <module.jacoco.skip>false</module.jacoco.skip>
            </properties>
        </profile>
        <profile>
            <id>release</id>
            <properties>
                <maven.javadoc.quite>true</maven.javadoc.quite>
                <maven.javadoc.skip>false</maven.javadoc.skip>
            </properties>
        </profile>
    </profiles>
</project>

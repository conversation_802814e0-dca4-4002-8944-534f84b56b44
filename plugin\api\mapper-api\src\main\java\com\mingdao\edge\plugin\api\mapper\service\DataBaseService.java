package com.mingdao.edge.plugin.api.mapper.service;

import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Map;

/**
 * 动态SQL服务接口
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
public interface DataBaseService {

    /**
     * 执行查询SQL
     *
     * @param sql SQL语句（使用MyBatis参数格式，如：SELECT * FROM table WHERE name = #{name}）
     * @param params SQL参数
     * @return 查询结果列表
     */
    List<Map<String, Object>> select(String sql, Map<String, Object> params);

    /**
     * 执行分页查询SQL
     *
     * @param sql SQL语句（使用MyBatis参数格式）
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页大小
     * @param params SQL参数
     * @return 分页结果
     */
    IPage<Map<String, Object>> selectPageBySql(String sql, Integer pageNum, Integer pageSize, Map<String, Object> params);

    /**
     * 执行插入SQL
     *
     * @param sql SQL语句（使用MyBatis参数格式）
     * @param params SQL参数
     * @return 生成的主键ID
     */
    Long insert(String sql, Map<String, Object> params);

    /**
     * 执行更新SQL
     *
     * @param sql SQL语句（使用MyBatis参数格式）
     * @param params SQL参数
     * @return 影响行数
     */
    int update(String sql, Map<String, Object> params);

    /**
     * 执行删除SQL
     *
     * @param sql SQL语句（使用MyBatis参数格式）
     * @param params SQL参数
     * @return 影响行数
     */
    int delete(String sql, Map<String, Object> params);

    /**
     * 批量删除指定ID的记录
     *
     * @param tableName 表名
     * @param ids 主键ID列表
     * @return 影响行数
     */
    int deleteByIds(String tableName, List<Long> ids);

    /**
     * 执行创建表SQL
     *
     * @param sql 创建表的SQL语句（CREATE TABLE语句）
     * @param params SQL参数（如果有）
     * @return 是否创建成功
     */
    boolean createTable(String sql, Map<String, Object> params);

    /**
     * 检查表中是否存在指定主键值的记录
     *
     * @param tableName 表名
     * @param primaryKey 主键列名
     * @param primaryValue 主键值
     * @return 如果存在记录返回true，否则返回false
     */
    boolean exists(String tableName, String primaryKey, Object primaryValue);

    /**
     * 查询单行数据
     *
     * @param sql SQL语句（使用MyBatis参数格式）
     * @param params SQL参数
     * @return 单行数据，如果没有找到返回null
     */
    Map<String, Object> selectOne(String sql, Map<String, Object> params);

    /**
     * 执行COUNT查询
     *
     * @param sql SQL语句（使用MyBatis参数格式）
     * @param params SQL参数
     * @return 查询结果数量
     */
    long count(String sql, Map<String, Object> params);

    /**
     * 清空指定表的数据
     *
     * @param tableName 表名
     */
    void truncateTable(String tableName);
}

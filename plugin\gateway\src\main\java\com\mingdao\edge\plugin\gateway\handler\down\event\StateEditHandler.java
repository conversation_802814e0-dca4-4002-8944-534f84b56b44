package com.mingdao.edge.plugin.gateway.handler.down.event;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.core.common.dto.BaseResponse;
import com.mingdao.edge.core.common.exception.Assert;
import com.mingdao.edge.plugin.api.gateway.EdgeStateService;
import com.mingdao.edge.plugin.api.gateway.GatewayDownHandler;
import com.mingdao.edge.plugin.api.gateway.constants.GatewayConstants;
import com.mingdao.edge.plugin.api.gateway.dto.CommandDto;
import com.mingdao.edge.plugin.api.gateway.dto.EdgeState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
@Component(GatewayConstants.GATEWAY_CMD_BEAN_PREFIX + "state_edit")
public class StateEditHandler implements GatewayDownHandler {

    @Resource
    private EdgeStateService edgeStateService;

    @Override
    public Object process(String messageId, CommandDto<?> commandDto) {
        BaseResponse<List<Object>> response = new BaseResponse<>();
        try {
            Assert.notNull(commandDto.getReplyTopic(), "参数不能为空");

            JSONObject jsonObject = (JSONObject) commandDto.getData();
            EdgeState edgeState = jsonObject.toJavaObject(EdgeState.class);

            edgeStateService.putState(edgeState);

        } catch (Exception e) {
            log.error(StrUtil.format("执行指令异常 {} {}", "state_edit", e.getMessage()), e);
            response.setCode(1);
            response.setMsg(e.getMessage());
        }

        return null;
    }
}

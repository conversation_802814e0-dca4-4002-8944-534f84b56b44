package com.mingdao.edge.plugin.data.sync.property;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Configuration
@ConfigurationProperties(prefix = "uploader", ignoreInvalidFields = true)
@ConditionalOnProperty(prefix = "uploader", name = "type", havingValue = "yuze")
public class YuzeProperty {
    @Getter
    @Setter
    private String url;
    @Getter
    @Setter
    private String appId;
    @Getter
    @Setter
    private String key;
    private String secret;

    public String getSignature(long timestamp, String nonce) {
        String data = "appid=" + appId + "&timestamp=" + timestamp + "&nonce=" + nonce;
        return generateHMAC(data);
    }
    private String generateHMAC(String data) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(this.getKey().getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);
            byte[] hash = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate HMAC", e);
        }
    }

}

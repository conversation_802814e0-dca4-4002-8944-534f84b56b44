package com.mingdao.edge.plugin.cloud.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "mingdao.api")
public class CloudApiProperties {
    private String domain;
    private String ssoType;
    private String ssoUrl;
    private String tasksInitPath;
    private String initPath;
    private String initUser;
    private String initPassword;
}

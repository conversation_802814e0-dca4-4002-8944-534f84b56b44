<?xml version="1.0" encoding="UTF-8"?>
<included>
    <!-- console-appender properties -->

	<include resource="default.xml"/>

	<!-- appender：主日志文件 -->
	<appender name="ALL" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!-- 日志文件路径及文件名 -->
		<file>${LOG_HOME}/all.log</file>
		<!-- 内容编码及格式 -->
		<encoder>
			<pattern>${FILE_LOG_PATTERN}</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<!-- 日志记录器的滚动策略，按日期，按大小记录 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!--路径-->
			<fileNamePattern>${LOG_HOME}/%d{yyyy-MM,aux}/%d{yyyy-MM-dd}-info-%i.log.gz</fileNamePattern>
			<!-- 清除m天前的日志 -->
			<maxHistory>15</maxHistory>
			<!-- 单文件超过后归档压缩 -->
			<maxFileSize>1GB</maxFileSize>
			<!-- 总大小 -->
			<totalSizeCap>10GB</totalSizeCap>
		</rollingPolicy>
	</appender>
</included>

package com.mingdao.edge.plugin.data.source.driver.mssql.config;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alipay.sofa.runtime.api.annotation.SofaReference;
import com.mingdao.edge.core.api.biz.BizCache;
import com.mingdao.edge.core.api.biz.BizInitializer;
import com.mingdao.edge.core.common.context.SpringContextUtils;
import com.mingdao.edge.core.common.exception.Assert;
import com.mingdao.edge.plugin.data.source.driver.api.dto.MSSQLProperty;
import com.mingdao.edge.plugin.data.source.driver.mssql.holder.MSSQLPropertyHolder;
import com.mingdao.edge.plugin.data.source.driver.mssql.service.MSSQLConnectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * description BizInitializerImpl
 *
 * <AUTHOR>
 * @since 2022/7/12 17:49
 */
@Slf4j
@Component
@Order(9999)
public class BizInitializerImpl implements BizInitializer {

    @Value("${app.version}")
    private String appVersion;

    @SofaReference
    private BizCache bizCache;
    @Resource
    private MSSQLConnectionService mssqlConnectionService;
    private MSSQLProperty mssqlProperty;

    @PostConstruct
    @Override
    public void init() {
        String applicationName = SpringContextUtils.getApplicationName();
        log.info("[DEBUG]开始初始化{} {}", applicationName, appVersion);
        Object cacheObject = bizCache.getInitParams(applicationName, appVersion, BizCache.kEY_INIT_PARAMS);
        Assert.notNull(cacheObject, "无法获取启动参数");
        String json = cacheObject.toString();
        if (!StrUtil.isEmpty(json)) {
            mssqlProperty = JSON.parseObject(json, MSSQLProperty.class);

            // 设置到单例中
            MSSQLPropertyHolder.setInstance(mssqlProperty);

            // 初始化数据库连接
            List<MSSQLProperty> configs = new ArrayList<>();
            configs.add(mssqlProperty);
            //databaseConnectionManager.initializeConnections(configs);

            // 初始化CRUD服务
            mssqlConnectionService.initializeDatabaseConnections(configs);
            //mssqlCrudService.initialize(mssqlProperty);

            log.info("MSSQL connection initialized for host: {} and database: {}",
                    mssqlProperty.getMssqlHost(), mssqlProperty.getDataBaseName());
        }

        log.info("[DEBUG]完成初始化{} {}", applicationName, appVersion);
    }
}

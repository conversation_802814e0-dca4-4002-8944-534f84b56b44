//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.mingdao.edge.core.common.config;

import com.fasterxml.jackson.databind.Module;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

@Configuration
public class JacksonAutoConfiguration {
    public JacksonAutoConfiguration() {
    }

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer defaultObjectMapperBuilderCustomizer() {
        return (jacksonObjectMapperBuilder) -> {
            jacksonObjectMapperBuilder.featuresToEnable(new Object[]{SerializationFeature.WRITE_DATES_AS_TIMESTAMPS});
            jacksonObjectMapperBuilder.timeZone("GMT+8");
            jacksonObjectMapperBuilder.modulesToInstall(new Module[]{new Jdk8Module()});
            JavaTimeModule javaTimeModule = new JavaTimeModule();
            javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeJacksonSerializer());
            javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeJacksonDeserializer());
            jacksonObjectMapperBuilder.modulesToInstall(new Module[]{javaTimeModule});
        };
    }
}

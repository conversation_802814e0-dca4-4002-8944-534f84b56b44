package com.mingdao.edge.plugin.api.mapper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@TableName("tb_data_log")
@Entity(name = "tb_data_log")
public class DataLog {

    @Id
    @TableId(type = IdType.AUTO)
    private Long id;

    private String sysOrg;
    private String bizType;
    private String bizId;
    private Long dataId;
    @TableField("MD_STATE")
    private String MD_STATE;
    @TableField("MD_DT_NAME")
    private String MD_DT_NAME;
    private Long createTime;
    private Long updateTime;
}

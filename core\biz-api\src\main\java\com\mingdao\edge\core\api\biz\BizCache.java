package com.mingdao.edge.core.api.biz;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * description: BizCache
 * date: 2022/7/12 18:40
 * author: guanjinhua
 * version: 1.0
 */
public interface BizCache {

    /**
     * 程序初始化缓存key
     */
    String kEY_INIT_PARAMS = "initParams";

    /**
     * 获取程序缓存
     * @param bizName
     * @param bizVersion
     * @param cacheName
     * @return
     */
    <T> T getInitParams(String bizName, String bizVersion, String cacheName);

    /**
     * 设置程序缓存
     * @param bizName
     * @param bizVersion
     * @param cacheName
     * @param data
     */
    void setInitParams(String bizName, String bizVersion, String cacheName, Object data);

    /**
     * 查询所有缓存
     * @return
     */
    Map<String, ConcurrentHashMap<String, Object>> listAll();

    /**
     * 清楚指定的一级缓存
     */
    void remove(String firstLevelCacheKey);

    /**
     * 清除指定一级缓存下的指定二级缓存
     */
    void remove(String firstLevelCacheKey, String secondCacheKey);

    <T> T getValue(String bizName, String bizVersion, String key, Class<T> clazz);
    <T> void setValue(String bizName, String bizVersion, String key, T value);
    <T> void setValue(String bizName, String bizVersion, String key, T value, int timeout);
}

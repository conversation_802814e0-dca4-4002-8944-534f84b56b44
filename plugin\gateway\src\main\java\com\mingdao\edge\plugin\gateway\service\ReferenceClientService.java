package com.mingdao.edge.plugin.gateway.service;

import com.mingdao.edge.core.api.biz.BizExchangeService;
import com.mingdao.edge.core.common.service.AbstractReferenceServiceHolder;
import org.springframework.stereotype.Service;

/**
 * description ReferenceClientUtils
 *
 * <AUTHOR>
 * @since 2022/7/19 8:54
 */
@Service
@SuppressWarnings({"rawtypes", "unchecked"})
public class ReferenceClientService extends AbstractReferenceServiceHolder<BizExchangeService> {
}

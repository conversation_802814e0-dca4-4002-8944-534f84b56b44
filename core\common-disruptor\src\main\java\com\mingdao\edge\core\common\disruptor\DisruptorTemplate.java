package com.mingdao.edge.core.common.disruptor;

import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.WorkHandler;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.util.DaemonThreadFactory;
import com.mingdao.edge.core.common.disruptor.event.DisruptorEvent;
import com.mingdao.edge.core.common.disruptor.factory.DisruptorEventFactory;
import com.mingdao.edge.core.common.disruptor.handler.DisruptorEventHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;

import java.util.Arrays;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Disruptor模板类，提供便捷的操作方法
 * @param <T> 事件数据类型
 */
@Slf4j
public class DisruptorTemplate<T> implements InitializingBean, DisposableBean {
    private final Disruptor<DisruptorEvent<T>> disruptor;
    private RingBuffer<DisruptorEvent<T>> ringBuffer;
    private final ExecutorService executor;
    private final DisruptorEventHandler<T> eventHandler;
    private final int consumerThreads;

    public DisruptorTemplate(int bufferSize, int consumerThreads, DisruptorEventHandler<T> eventHandler) {
        this.eventHandler = eventHandler;
        this.consumerThreads = consumerThreads;
        this.executor = Executors.newFixedThreadPool(consumerThreads, DaemonThreadFactory.INSTANCE);
        this.disruptor = new Disruptor<>(
                new DisruptorEventFactory<>(),
                bufferSize,
                DaemonThreadFactory.INSTANCE
        );

        // 创建多个消费者
        WorkHandler<DisruptorEvent<T>>[] handlers = new WorkHandler[this.consumerThreads];
        Arrays.fill(handlers, (WorkHandler<DisruptorEvent<T>>) event -> {
            try {
                eventHandler.handle(event);
            } catch (Exception e) {
                log.error("处理事件时发生错误", e);
            }
        });

        // 设置多消费者
        this.disruptor.handleEventsWithWorkerPool(handlers);
    }

    @Override
    public void afterPropertiesSet() {
        this.ringBuffer = disruptor.start();
    }

    /**
     * 发布事件
     * @param data 事件数据
     */
    public void publish(T data) {
        ringBuffer.publishEvent((event, sequence) -> {
            event.setData(data);
            event.setSequence(sequence);
        });
    }

    @Override
    public void destroy() {
        disruptor.shutdown();
        executor.shutdown();
    }

    /**
     * 关闭Disruptor
     */
    public void shutdown() {
        destroy();
    }
}

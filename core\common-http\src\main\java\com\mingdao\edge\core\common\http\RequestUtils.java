package com.mingdao.edge.core.common.http;

import com.mingdao.edge.core.common.context.SpringContextUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

public class RequestUtils {

    public static Object doGet(String url) {
        RestTemplate restTemplate = SpringContextUtils.getBean("okRestTemplate");
        return restTemplate.getForObject(url, Object.class);
    }

    public static <R> R doGet(String url, Map<String, Object> params, HttpHeaders requestHeaders, ParameterizedTypeReference<R> parameterizedTypeReference) {
        HttpEntity<Object> httpEntity = new HttpEntity<>(null, requestHeaders);
        return request(url, HttpMethod.GET, httpEntity, parameterizedTypeReference, params);
    }

    public static <R> R doGet(String url, Map<String, Object> params, HttpHeaders requestHeaders, Class<R> responseType) {
        HttpEntity<Object> httpEntity = new HttpEntity<>(null, requestHeaders);
        return request(url, HttpMethod.GET, httpEntity, responseType, params);
    }

    public static <R, P> R doPost(String url, P params, HttpHeaders requestHeaders, ParameterizedTypeReference<R> parameterizedTypeReference) {
        HttpEntity<P> httpEntity = new HttpEntity<>(params, requestHeaders);
        return request(url, HttpMethod.POST, httpEntity, parameterizedTypeReference, null);
    }

    public static <R, P> R doPost(String url, P params, HttpHeaders requestHeaders, Class<R> responseType) {
        HttpEntity<P> httpEntity = new HttpEntity<>(params, requestHeaders);
        return request(url, HttpMethod.POST, httpEntity, responseType, null);
    }

    public static  <R, P> R doPost(String url, P params, Class<R> responseType) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<P> httpEntity = new HttpEntity<>(params, httpHeaders);
        return request(url, HttpMethod.POST, httpEntity, responseType, null);
    }

    public static  <R, P> ResponseEntity<R> doPostForm(String url, P params, Class<R> responseType) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<P> httpEntity = new HttpEntity<>(params, httpHeaders);
        return getResponse(url, HttpMethod.POST, httpEntity, responseType, null);
    }

    public static <R, P> ResponseEntity<R> postByTypeReference(String url, P params, HttpHeaders requestHeaders, ParameterizedTypeReference<R> parameterizedTypeReference) {
        HttpEntity<P> httpEntity = new HttpEntity<>(params, requestHeaders);
        return getResponseByTypeReference(url, HttpMethod.POST, httpEntity, parameterizedTypeReference, null);
    }

	public static <R, P> R doPostFile(String url, P params, HttpHeaders requestHeaders, Class<R> responseType) {
    	if(requestHeaders == null) {
			requestHeaders = new HttpHeaders();
		}
		requestHeaders.setContentType(MediaType.MULTIPART_FORM_DATA);
		requestHeaders.setConnection("Keep-Alive");
		requestHeaders.setCacheControl("no-cache");
		HttpEntity<P> httpEntity = new HttpEntity<>(params, requestHeaders);
		return request(url, HttpMethod.POST, httpEntity, responseType, null);
	}


    /**
     * 有泛型的情况下使用
     * @param url 地址
     * @param requestEntity 请求实体
     * @param parameterizedTypeReference 泛型 sample: new ParameterizedTypeReference<BaseResponse<Foo>>() {}
     * @param <P> 参数类型
     * @param <R> 响应类型
     * @return 响应数据
     */
    public static <R, P> R request(String url,
                            HttpMethod method,
                            HttpEntity<P> requestEntity,
                            ParameterizedTypeReference<R> parameterizedTypeReference,
                            Map<String, Object> uriVariables) {
		if(uriVariables == null) {
			uriVariables = new HashMap<>();
		}
        RestTemplate restTemplate = SpringContextUtils.getBean("okRestTemplate");
        ResponseEntity<R> responseEntity = restTemplate.exchange(
                url,
                method,
                requestEntity,
                parameterizedTypeReference,
                uriVariables
        );
        return responseEntity.getBody();
    }

    /**
     * 有泛型的情况下使用
     * @param url 地址
     * @param requestEntity 请求实体
     * @param responseType 泛型
     * @param <P> 参数类型
     * @param <R> 响应类型
     * @return 响应数据
     */
    public static <R, P> R request(String url,
                            HttpMethod method,
                            HttpEntity<P> requestEntity,
                            Class<R> responseType,
                            Map<String, Object> uriVariables) {
		if(uriVariables == null) {
			uriVariables = new HashMap<>();
		}
        RestTemplate restTemplate = SpringContextUtils.getBean("okRestTemplate");
        ResponseEntity<R> responseEntity = restTemplate.exchange(
                url,
                method,
                requestEntity,
                responseType,
                uriVariables
        );
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            return responseEntity.getBody();
        } else if (responseEntity.getStatusCode().is4xxClientError()) {

        }
        return responseEntity.getBody();
    }

    /**
     * 有泛型的情况下使用
     * @param url 地址
     * @param requestEntity 请求实体
     * @param classType 泛型
     * @param <P> 参数类型
     * @param <R> 响应类型
     * @return 响应数据
     */
    public static <R, P> ResponseEntity<R> getResponse(String url,
                                   HttpMethod method,
                                   HttpEntity<P> requestEntity,
                                   Class<R> classType,
                                   Map<String, Object> uriVariables) {
        if(uriVariables == null) {
            uriVariables = new HashMap<>();
        }
        RestTemplate restTemplate = SpringContextUtils.getBean("okRestTemplate");
        return restTemplate.exchange(
                url,
                method,
                requestEntity,
                classType,
                uriVariables
        );
    }

    /**
     * 有泛型的情况下使用
     * @param url 地址
     * @param requestEntity 请求实体
     * @param parameterizedTypeReference 泛型
     * @param <P> 参数类型
     * @param <R> 响应类型
     * @return 响应数据
     */
    public static <R, P> ResponseEntity<R> getResponseByTypeReference(String url,
                                                       HttpMethod method,
                                                       HttpEntity<P> requestEntity,
                                                       ParameterizedTypeReference<R> parameterizedTypeReference,
                                                       Map<String, Object> uriVariables) {
        if(uriVariables == null) {
            uriVariables = new HashMap<>();
        }
        RestTemplate restTemplate = SpringContextUtils.getBean("okRestTemplate");
        return restTemplate.exchange(
                url,
                method,
                requestEntity,
                parameterizedTypeReference,
                uriVariables
        );
    }
}

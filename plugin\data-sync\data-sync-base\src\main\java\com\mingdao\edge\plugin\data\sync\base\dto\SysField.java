package com.mingdao.edge.plugin.data.sync.base.dto;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Data
@AllArgsConstructor
public class SysField {
    private String fieldName;
    /**
     * VARCHAR(32)
     */
    private String fieldType;
    private Boolean notNull;

    public String toDDL() {
        return StrUtil.format("\n {} {} {}", fieldName, "VARCHAR(32)", notNull ? "NOT NULL": "NULL");
    }
}

app.version: ${project.version}

server:
  port: ${data_sync_port:9204}

spring:
  application:
    name: ${project.name}
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration

logging:
  file:
    path: ./logs/${project.name}

uploader:
  type: yuze
  url: http://************:81
  appId: app2025032501
  key: P0Ryn23QUHJ56Z6PlZbfdtC

kingdee:
  AcctID: 676fc285994b12
  AppID: 300857_x+bAT6vLyqn5WXVI6+SDycUt3u0V2CtG
  AppSec: bf240dd6a147408680665b96a36bd111
  UserName: yichejian
  UserPsw: mimaM963.
  LCID: 2052
  ServerUrl: https://k3.sunko.cn:89/k3cloud/

third-part:
  api:
    ip: up530.cn:58029
    authority_code:


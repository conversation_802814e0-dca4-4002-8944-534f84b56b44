package com.mingdao.edge.plugin.data.sync.base.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.plugin.api.data.sync.constants.DataState;
import com.mingdao.edge.plugin.api.data.sync.constants.MDColumn;
import com.mingdao.edge.plugin.api.data.sync.dto.ColTemp;
import com.mingdao.edge.plugin.api.data.sync.dto.WarehouseProject;
import com.mingdao.edge.plugin.api.gateway.dto.DataDto;
import com.mingdao.edge.plugin.api.gateway.dto.DownMessage;
import com.mingdao.edge.plugin.api.gateway.dto.EventMessage;
import com.mingdao.edge.plugin.data.source.driver.api.exception.ApiException;
import com.mingdao.edge.plugin.data.sync.base.dto.SysField;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Slf4j
public abstract class AbstractDataSyncDown extends AbstractDataSync {

    protected final String WAREHOUSE_PROJECT_INSERT_SQL = "INSERT INTO tb_warehouse_project (MD_MD5, TASKORGTITLE, STATUS_datasource, AUTOID, SYS_MODIFY_DATE, SYS_MODIFIER, HISTORYSTATUS, NAME, QUERYEXPRESSION, CODE, STATUS, TASKORGNAME, SYS_CREATE_DATE, TASKORGID, TYPE, SYS_CREATOR, TRANSFERFIELD) VALUES (#{params.MD_MD5}, #{params.TASKORGTITLE}, #{params.STATUS_datasource}, #{params.AUTOID}, #{params.SYS_MODIFY_DATE}, #{params.SYS_MODIFIER}, #{params.HISTORYSTATUS}, #{params.NAME}, #{params.QUERYEXPRESSION}, #{params.CODE}, #{params.STATUS}, #{params.TASKORGNAME}, #{params.SYS_CREATE_DATE}, #{params.TASKORGID}, #{params.TYPE}, #{params.SYS_CREATOR}, #{params.TRANSFERFIELD})";

    protected final ConcurrentHashMap<String, WarehouseProject> WAREHOUSE_PROJECT = new ConcurrentHashMap<>();

    public AbstractDataSyncDown(String moduleName, String pageName, Integer pageSize) {
        super(moduleName, pageName, pageSize);
    }

    @Override
    public void init(JSONObject taskInfo) {
        super.init(taskInfo);
        createWarehouseProjectTable();
    }

    @Override
    protected List<SysField> getSysFields() {
        List<SysField> list = super.getSysFields();
        list.add(new SysField(MDColumn.MD_WAREHOUSE_PROJECT_ID, "VARCHAR(16)", true));
        return list;
    }

    @Override
    public Long convertDataAndSave(JSONObject data, Map<String, ColTemp> fieldMap) {
        String projectAutoId = data.getString(MDColumn.MD_WAREHOUSE_PROJECT_ID);
        WarehouseProject warehouseProject = WAREHOUSE_PROJECT.get(projectAutoId);
        Map<String, Object> result = new LinkedHashMap<>();

        // 下行是通过方案字段来映射
        warehouseProject.getTransferFieldList().forEach(field -> {
            // 这是云端字段名称
            String targetFieldName = field.getTargetField();
            // 这是客户侧字段名称
            String sourceFieldName = field.getSourceField();
            // 云端的值
            Object sourceValue = data.get(targetFieldName);
            //// 数字类型时，如果值为空字符串，则设为null
            ColTemp colTemp = fieldMap.get(sourceFieldName);
            if (Boolean.TRUE.equals(colTemp.getIsNumber())
                    && sourceValue instanceof String
                    && ObjectUtil.isEmpty(sourceValue)) {
                sourceValue = null;
            }
            if (!convertSpecField(sourceFieldName, sourceValue, result)) {
                result.put(sourceFieldName, sourceValue);
            }
        });

        addSysFields(dtName, result, data);
        Long id = null;
        try {
            id = saveData(result);
        } catch (Exception e) {
            if (result.containsKey("autoid")) {
                JSONObject updateObj = new JSONObject();
                String autoId = result.get("autoid").toString();
                updateObj.put("AUTOID", autoId);
                setUpdateObjectFail(String.valueOf(warehouseProject.getAUTOID()), id, updateObj, "保存数据失败：" + e.getMessage());
                updateCloudStatus(updateObj);
            }
        }
        return id;
    }

    @Override
    public void transferData(EventMessage<JSONObject> message) {
        JSONObject parsedData = message.getData();
        Object id = parsedData.get(MDColumn.ID);
        try {
            DownMessage<?> data = doTransDownData(parsedData);
            // 修改状态：上传成功
            updateState(id, DataState.UPLOAD_SUCCESS);
            // 修改状态：同步成功或失败
            transferDataCallback(data);
        } catch (Exception e) {
            updateState(id, DataState.UPLOAD_FAIL);
        }
    }

    @Override
    public void transferDataCallback(DownMessage<?> downMessage) {
        DataDto<?> data1 = downMessage.getDataDto();
        String id = data1.getId();
        if (downMessage.getCode() == 0) {
            updateState(id, DataState.SYNC_SUCCESS);
        } else {
            updateState(id, DataState.SYNC_FAIL);
            addLogMsg(Long.parseLong(id), downMessage.getMsg());
        }
    }

    protected abstract DownMessage<?> doTransDownData(JSONObject parsedData);

    protected abstract JSONArray getPushDataList(WarehouseProject warehouseproject, Integer page, Integer limit);

    @Override
    protected void addSysFields(String dtName, Map<String, Object> result, JSONObject item) {
        super.addSysFields(dtName, result, item);
        result.put(MDColumn.MD_WAREHOUSE_PROJECT_ID, item.getString(MDColumn.MD_WAREHOUSE_PROJECT_ID));
    }

    protected void createWarehouseProjectTable() {
        String createTableSql = "CREATE TABLE if not exists tb_warehouse_project (\n" +
                "    id bigint auto_increment primary key,\n" +
                "    AUTOID VARCHAR(32),\n" +
                "    TASKORGTITLE VARCHAR(128),\n" +
                "    STATUS_datasource VARCHAR(32),\n" +
                "    SYS_MODIFY_DATE VARCHAR(32),\n" +
                "    SYS_MODIFIER VARCHAR(32),\n" +
                "    HISTORYSTATUS INT,\n" +
                "    NAME VARCHAR(32),\n" +
                "    QUERYEXPRESSION VARCHAR(4000),\n" +
                "    CODE VARCHAR(128),\n" +
                "    STATUS INT,\n" +
                "    TASKORGNAME VARCHAR(128),\n" +
                "    SYS_CREATE_DATE VARCHAR(32),\n" +
                "    TASKORGID VARCHAR(32),\n" +
                "    TYPE INT,\n" +
                "    SYS_CREATOR VARCHAR(32),\n" +
                "    TRANSFERFIELD TEXT,\n" +
                "    MD_MD5 VARCHAR(32)\n" +
                ");";
        dataBaseService.createTable(createTableSql, null);
    }

    protected void saveWarehouseProject(JSONObject warehouseProject) {
        String md5 = DigestUtil.md5Hex(warehouseProject.toJSONString());

        boolean exists = dataBaseService.exists("tb_warehouse_project", MDColumn.MD5, md5);
        if (!exists) {
            warehouseProject.put(MDColumn.MD5, md5);
            dataBaseService.insert(WAREHOUSE_PROJECT_INSERT_SQL, warehouseProject);
        }

        //String dSql = "delete from tb_warehouse_project where " + MDColumn.MD5 + " = #{params.md5}";
        //dataBaseService.delete(dSql, md5Params);
    }
    protected JSONObject getWarehouseProject(String autoId) {
        String dSql = "SELECT id, AUTOID, TASKORGTITLE, STATUS_datasource, SYS_MODIFY_DATE, SYS_MODIFIER, HISTORYSTATUS, NAME, QUERYEXPRESSION, CODE, STATUS, TASKORGNAME, SYS_CREATE_DATE, TASKORGID, TYPE, SYS_CREATOR, CAST(TRANSFERFIELD AS VARCHAR) AS TRANSFERFIELD, MD_MD5 FROM tb_warehouse_project WHERE AUTOID = #{params.autoId}";
        Map<String, Object> params = new HashMap<>(1);
        params.put("autoId", autoId);
        Map<String, Object> data = dataBaseService.selectOne(dSql, params);
        return new JSONObject(data);
    }

    protected abstract JSONObject getData(JSONObject dataObj) throws ApiException;
    protected abstract JSONObject addData(JSONObject dataObj) throws ApiException;
    protected abstract JSONObject editData(JSONObject dataObj) throws ApiException;

    /**
     * 同步成功时组织参数回调
     */
    protected abstract void setUpdateObjectSuccess(String warehouseProjectId, Long dataId, JSONObject updateObj, JSONObject dataObj, JSONObject result);

    /**
     * 同步失败时组织参数回调
     */
    protected abstract void setUpdateObjectFail(String warehouseProjectId, Long dataId, JSONObject updateObj, String msg);

    protected abstract void updateCloudStatus(JSONObject updateObj);
}

package com.mingdao.edge.plugin.data.source.driver.mssql.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.mingdao.edge.plugin.data.source.driver.mssql.holder.MSSQLPropertyHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * 动态MyBatis配置类
 * 为动态MSSQL数据源配置MyBatis
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Configuration
@MapperScan(basePackages = "com.mingdao.edge.plugin.data.source.driver.mssql.mapper",
           sqlSessionFactoryRef = "mssqlSqlSessionFactory")
public class DynamicMyBatisConfig {

    @Autowired
    private DatabaseConnectionManager databaseConnectionManager;

    /**
     * 获取当前数据源
     */
    private DataSource getCurrentDataSource() {
        String databaseName = MSSQLPropertyHolder.getDatabaseName();
        if (databaseName == null) {
            log.warn("No database name found in MSSQLPropertyHolder");
            return null;
        }

        DataSource dataSource = databaseConnectionManager.getDataSource(databaseName);
        if (dataSource == null) {
            log.warn("No data source found for database: {}", databaseName);
        }

        return dataSource;
    }

    /**
     * MSSQL SqlSessionFactory
     */
    @Bean("mssqlSqlSessionFactory")
    @Lazy
    public SqlSessionFactory mssqlSqlSessionFactory() throws Exception {
        DataSource dataSource = getCurrentDataSource();
        if (dataSource == null) {
            log.error("Cannot create SqlSessionFactory: no data source available");
            throw new RuntimeException("No data source available for MyBatis configuration");
        }

        SqlSessionFactoryBean factoryBean = new SqlSessionFactoryBean();
        factoryBean.setDataSource(dataSource);

        // 设置MyBatis配置
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setLogImpl(org.apache.ibatis.logging.stdout.StdOutImpl.class);
        factoryBean.setConfiguration(configuration);

        // 添加MyBatis Plus分页插件 - 简化配置
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor(DbType.SQL_SERVER2005);
        paginationInterceptor.setOptimizeJoin(false);
        interceptor.addInnerInterceptor(paginationInterceptor);
        factoryBean.setPlugins(interceptor);

        log.info("MyBatis Plus分页插件已配置，数据库类型: SQL_SERVER2005");

        // 设置Mapper XML文件位置
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        factoryBean.setMapperLocations(resolver.getResources("classpath:mapper/*.xml"));

        log.info("MSSQL SqlSessionFactory configured for database: {}", MSSQLPropertyHolder.getDatabaseName());
        return factoryBean.getObject();
    }

    /**
     * MSSQL SqlSessionTemplate
     */
    @Bean("mssqlSqlSessionTemplate")
    @Lazy
    public SqlSessionTemplate mssqlSqlSessionTemplate(SqlSessionFactory mssqlSqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(mssqlSqlSessionFactory);
    }


}

package com.mingdao.edge.plugin.data.source.driver.mssql.config;

import com.mingdao.edge.plugin.data.source.driver.api.dto.MSSQLProperty;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class DatabaseConnectionManager {
    private final Map<String, HikariDataSource> dataSources = new ConcurrentHashMap<>();

    public void initializeConnections(List<MSSQLProperty> configs) {
        // 关闭现有连接
        closeAllConnections();

        // 初始化新连接
        for (MSSQLProperty config : configs) {
            try {
                config.buildUrl();
                HikariConfig hikariConfig = new HikariConfig();
                hikariConfig.setJdbcUrl(config.getUrl());
                hikariConfig.setUsername(config.getUserName());
                hikariConfig.setPassword(config.getPassword());
                hikariConfig.setDriverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver");

                //// 连接池配置
                //hikariConfig.setMaximumPoolSize(10);
                //hikariConfig.setMinimumIdle(5);
                //hikariConfig.setIdleTimeout(300000);
                //hikariConfig.setConnectionTimeout(20000);
                //hikariConfig.setMaxLifetime(1200000);

                log.info("Initializing DB: url={}, user={}, db={}", config.getUrl(), config.getUserName(), config.getDataBaseName());
                HikariDataSource dataSource = new HikariDataSource(hikariConfig);
                dataSources.put(getConnectionKey(config), dataSource);
                log.info("Successfully initialized database connection for: {}", config.getDataBaseName());
            } catch (Exception e) {
                log.error("Failed to initialize database connection for: " + config.getDataBaseName(), e);
            }
        }
    }

    /**
     * 通过数据库名称获取数据源
     * @param databaseName 数据库名称
     * @return 数据源
     */
    public DataSource getDataSource(String databaseName) {
        return dataSources.get(databaseName);
    }

    /**
     * 兼容旧版本的方法，忽略host参数
     * @param host 主机地址（忽略）
     * @param databaseName 数据库名称
     * @return 数据源
     * @deprecated 使用 getDataSource(String databaseName) 替代
     */
    @Deprecated
    public DataSource getDataSource(String host, String databaseName) {
        return getDataSource(databaseName);
    }

    private void closeAllConnections() {
        for (HikariDataSource dataSource : dataSources.values()) {
            if (dataSource != null && !dataSource.isClosed()) {
                dataSource.close();
            }
        }
        dataSources.clear();
    }

    private String getConnectionKey(MSSQLProperty config) {
        return config.getDataBaseName();
    }
}

com.alipay.sofa:
  boot:
    skip-jvm-reference-health-check: true

server:
  port: 1995
#  tomcat:
#    uri-encoding: utf-8
#    threads:
#      max: 1000
#    max-connections: 1500
#    accept-count: 500

spring:
  application:
    name: ${project.name}
  jmx:
    enabled: true
    default-domain: md-edge
  main:
    allow-bean-definition-overriding: true
  jpa:
    open-in-view: false
    hibernate:
      ddl-auto: update
      use-new-id-generator-mappings: false
    show-sql: false
    database-platform: org.hibernate.dialect.H2Dialect
  h2:
    console:
      enabled: true
      path: /md-edge
      settings:
        web-admin-password: md@edge
  flyway:
    baseline-on-migrate: true
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: org.h2.Driver
    url: jdbc:h2:file:./database/md-edge;DATABASE_TO_UPPER=FALSE;IGNORECASE=TRUE;AUTO_SERVER=TRUE;DB_CLOSE_ON_EXIT=FALSE;MODE=LEGACY;
    username: md-edge
    password: md@edge
    generate-unique-name: false
    name: ${spring.application.name}
    hikari:
      minimum-idle: 5
      idle-timeout: 60000
      maximum-pool-size: 10
      auto-commit: true
      pool-name: ${spring.application.name}
      max-lifetime: 180000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  servlet:
    multipart:
      max-file-size: 25MB
      max-request-size: 25MB
  sql:
    init:
      mode: always
      schema-locations: classpath:sql/schema.sql


mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.mingdao.edge.plugin.api.mapper.entity
  configuration:
    map-underscore-to-camel-case: true

pagehelper:
  helper-dialect: mariadb
  reasonable: true
  support-methods-arguments: true

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

#management:
#  endpoint:
#    beans:
#      enabled: true

logging:
  file:
    path: ./logs/${spring.application.name}


mingdao:
  edge:
    healthcheck:
      get-biz-info-interval: 300000
      check-interval: 30000
    cache:
      type: ehcache
      ehcache:
        cacheName: ${spring.application.name}_${project.version}
        offheap: 100
        disk: 1000
  api:
    domain: ${md_domain:https://transfer.mingdao-info.com}
    SSO_TYPE: tenant
    SSO_URL: ${md_sso_url:https://transfer.mingdao-info.com/oauth}
    initUser: ${md_init_user:9e917ea057594346b05104752a59dee9}
    initPassword: ${md_init_password:de26412b72acc1a969648a48d4d328e00ff1caf1d33be4dd8031b5f74bb02bc9}

disruptor:
  buffer-size: 1024
  consumer-threads: 1  # 配置消费者线程

package com.mingdao.edge.core.common.http;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "okhttp", ignoreInvalidFields = true)
public class OkHttpProperties {

    /**
     * 连接超时，默认 10 秒，0 表示没有超时限制
     */
    private Integer connectTimeout = 10;

    /**
     * 响应超时，默认 10 秒，0 表示没有超时限制
     */
    private Integer readTimeout = 10;

    /**
     * 写超时，默认 10 秒，0 表示没有超时限制
     */
    private Integer writeTimeout = 10;

    /**
     * 连接池中整体的空闲连接的最大数量，默认 5 个连接数
     */
    private Integer maxIdleConnections = 5;

    /**
     * 连接空闲时间最大时间，单位秒，默认 300 秒
     */
    private Long keepAliveDuration = 300L;

    public Integer getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(Integer connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public Integer getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(Integer readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Integer getWriteTimeout() {
        return writeTimeout;
    }

    public void setWriteTimeout(Integer writeTimeout) {
        this.writeTimeout = writeTimeout;
    }

    public Integer getMaxIdleConnections() {
        return maxIdleConnections;
    }

    public void setMaxIdleConnections(Integer maxIdleConnections) {
        this.maxIdleConnections = maxIdleConnections;
    }

    public Long getKeepAliveDuration() {
        return keepAliveDuration;
    }

    public void setKeepAliveDuration(Long keepAliveDuration) {
        this.keepAliveDuration = keepAliveDuration;
    }
}

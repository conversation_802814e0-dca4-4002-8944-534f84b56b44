package com.mingdao.edge.plugin.cloud.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/biz")
public class CommandController {

    @GetMapping
    @ResponseBody
    public Object getAllBizNames() {
        //BizMessageDTO messageDTO = new BizMessageDTO();
        //messageDTO.setSourceBiz(BizUnique.DATA_CONVERTOR);
        //messageDTO.setDeviceKey("");
        //messageDTO.setSourceUniqueId("edge-gateway");
        //messageDTO.setTargetBiz(BizUnique.DATA_COLLECTOR);

        //BaseEventDTO eventDTO = new BaseEventDTO(EventTypeConstants.DATA_UP, null);
        //messageDTO.setData(eventDTO);
        //
        //gatewayDataExchanger.exchange(messageDTO);
        //return ArkClient.getBizManagerService().getAllBizNames();
        return "0";
    }

}

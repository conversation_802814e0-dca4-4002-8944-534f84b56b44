package com.mingdao.edge.plugin.data.sync.context;

import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.plugin.api.data.sync.service.DataSyncService;
import lombok.Data;

import java.util.concurrent.ScheduledFuture;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class TaskContext {
    private String uniqueId;
    private JSONObject taskInfo;
    private DataSyncService dataSyncService;
    private ScheduledFuture<?> scheduledFuture;
    private Long lastDataTime;
    private String upTopic;
}

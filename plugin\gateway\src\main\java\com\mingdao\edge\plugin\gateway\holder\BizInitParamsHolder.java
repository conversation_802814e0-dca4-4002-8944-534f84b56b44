package com.mingdao.edge.plugin.gateway.holder;

import com.mingdao.edge.core.common.cache.EdgeCacheManager;
import com.mingdao.edge.plugin.api.gateway.dto.DataTaskEdgeBiz;
import com.mingdao.edge.plugin.gateway.constants.BizConstant;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Service
public class BizInitParamsHolder {

    @Resource
    private EdgeCacheManager edgeCacheManager;

    public void setBizInitParams(DataTaskEdgeBiz bizInitParams) {
        String bizName = bizInitParams.getBIZ_NAME() + "_" + bizInitParams.getVERSION();
        edgeCacheManager.hSet(BizConstant.CACHE_KEY_BIZ_INIT_PARAMS, bizName, bizInitParams.getINIT_PARAMS());
    }

    public String getBizInitParams(String bizName, String version) {
        String cacheKey = bizName + "_" + version;
        return edgeCacheManager.hGet(BizConstant.CACHE_KEY_BIZ_INIT_PARAMS, cacheKey, String.class);
    }
}

package com.mingdao.edge.core.pulsar.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.core.common.context.SpringContextUtils;
import com.mingdao.edge.core.pulsar.config.PulsarClientInfo;
import com.mingdao.edge.core.pulsar.consumer.PulsarConsumerFactory;
import com.mingdao.edge.core.pulsar.vo.PulsarSubscriptionConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.*;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Pulsar客户端注册工具类
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Slf4j
public class PulsarClientUtil {

    private final static Object lock = new Object();

    // 缓存生产者，避免重复创建
    private final static Map<String, Producer<byte[]>> producers = new ConcurrentHashMap<>();

    /**
     * 注册Pulsar客户端（带认证）
     *
     * @param serviceUrl Pulsar服务地址
     * @param clientName 客户端名称
     * @param authToken  认证Token
     * @throws PulsarClientException Pulsar客户端异常
     */
    public static void registerPulsarClient(String serviceUrl, String clientName, String authToken) throws PulsarClientException {

        PulsarClientInfo clientInfo = new PulsarClientInfo();
        clientInfo.setServiceUrl(serviceUrl);
        clientInfo.setClientName(clientName);
        clientInfo.setAuthToken(authToken);

        // 创建客户端
        clientInfo.createPulsarClient();

        log.info("Pulsar客户端注册成功: serviceUrl={}, clientName={}",
                serviceUrl, clientName);

        String beanName = "pulsarClientInfo";
        PulsarClientInfo pulsarClientInfo = getPulsarClientInfo(beanName);
        if (pulsarClientInfo != null) {
            SpringContextUtils.unregisterBean(beanName);
            log.info("注销 {}", beanName);
        }
        SpringContextUtils.registerBean(beanName, clientInfo);
    }

    private static PulsarClientInfo getPulsarClientInfo(String beanName) {
        PulsarClientInfo pulsarClientInfo;
        try {
            pulsarClientInfo = SpringContextUtils.getBean(beanName);
        } catch (Exception e) {
            pulsarClientInfo = null;
        }
        return pulsarClientInfo;
    }

    /**
     * 创建单个主题的订阅配置
     *
     * @param topic            主题名称
     * @param subscriptionName 订阅名称
     * @param subscriptionType 订阅类型
     * @param messageListener  消息监听器
     * @return 订阅配置
     */
    public static PulsarSubscriptionConfig createSubscriptionConfig(
            String topic, String subscriptionName, SubscriptionType subscriptionType,
            MessageListener<byte[]> messageListener) {

        PulsarSubscriptionConfig config = new PulsarSubscriptionConfig();
        config.setTopic(topic);
        config.setSubscriptionName(subscriptionName);
        config.setSubscriptionType(subscriptionType);
        config.setMessageListener(messageListener);
        return config;
    }

    /**
     * 订阅主题
     *
     * @param subscriptionName 订阅名称
     * @param subscriptionType 订阅类型（可选，如果为null则使用默认Shared类型）
     * @param messageListener  消息监听器
     * @throws PulsarClientException Pulsar客户端异常
     */
    public static void subscribeNamespaceAllTopic(String tenantName,
                                                  String namespace,
                                                  String subscriptionName,
                                                  SubscriptionType subscriptionType,
                                                  MessageListener<byte[]> messageListener) throws PulsarClientException {

        PulsarConsumerFactory pulsarConsumerFactory = SpringContextUtils.getBean(PulsarConsumerFactory.class);

        pulsarConsumerFactory.createPatternConsumer(tenantName, namespace, subscriptionName, subscriptionType, messageListener);
    }



    /**
     * 获取或创建生产者
     *
     * @param topic 主题名称
     * @return 生产者
     */
    public static Producer<byte[]> getOrCreateProducer(String topic) {
        Producer<byte[]> producer = null;
        try {
            producer = producers.get(topic);
            if (producer == null || !producer.isConnected()) {
                synchronized (lock) {
                    if (producer != null) {
                        try {
                            producers.remove(topic).close();
                        } catch (PulsarClientException e) {
                            log.error("producer 关闭失败: topic" + e.getMessage());
                        }
                    }
                    producer = producers.get(topic);
                    if (producer == null) {
                        producer = createProducer(topic);
                        producers.put(topic, producer);
                    }
                }
            }
        } catch (Exception e) {
            log.error("producer <UNK>: topic" + e.getMessage());
        }
        return producer;
    }

    /**
     * 创建生产者
     *
     * @param topic 主题名称
     * @return 生产者
     */
    private static Producer<byte[]> createProducer(String topic) {
        try {
            log.info("创建Pulsar生产者，主题: {}", topic);
            PulsarClient pulsarClient = SpringContextUtils.getBean(PulsarClient.class);

            ProducerBuilder<byte[]> producerBuilder = pulsarClient.newProducer()
                    .topic(topic);

            Producer<byte[]> producer = producerBuilder.create();
            log.info("成功创建Pulsar生产者，主题: {}", topic);
            return producer;

        } catch (Exception e) {
            log.error("创建Pulsar生产者失败，主题: {}", topic, e);
            throw new RuntimeException("创建Pulsar生产者失败", e);
        }
    }

    /**
     * 同步发送消息
     *
     * @param topic   主题
     * @param payload 消息内容
     * @param <T>     消息类型
     * @return 消息ID
     */
    public static <T> MessageId send(String topic, T payload) {
        return send(topic, payload, null);
    }

    /**
     * 同步发送消息（带消息键）
     *
     * @param topic   主题
     * @param payload 消息内容
     * @param key     消息键
     * @param <T>     消息类型
     * @return 消息ID
     */
    public static  <T> MessageId send(String topic, T payload, String key) {
        try {
            Producer<byte[]> producer = PulsarClientUtil.getOrCreateProducer(topic);

            TypedMessageBuilder<byte[]> messageBuilder = producer.newMessage()
                    .value(convertToBytes(payload));

            // 设置消息键
            if (key != null && !key.isEmpty()) {
                messageBuilder.key(key);
            }

            MessageId messageId = messageBuilder.send();
            log.debug("Pulsar消息发送成功: topic={}, messageId={}", topic, messageId);
            return messageId;

        } catch (Exception e) {
            log.error("Pulsar消息发送失败: topic={}, error={}", topic, e.getMessage(), e);
            throw new RuntimeException("发送消息失败", e);
        }
    }

    public static <T> CompletableFuture<MessageId> sendAsync(String topic, T payload) {
        return sendAsync(topic, payload, null);
    }

    /**
     * 同步发送消息（带消息键）
     *
     * @param topic   主题
     * @param payload 消息内容
     * @param key     消息键
     * @param <T>     消息类型
     * @return 消息ID
     */
    public static <T> CompletableFuture<MessageId> sendAsync(String topic, T payload, String key) {
        try {
            Producer<byte[]> producer = PulsarClientUtil.getOrCreateProducer(topic);

            TypedMessageBuilder<byte[]> messageBuilder = producer.newMessage()
                    .value(convertToBytes(payload));

            // 设置消息键
            if (key != null && !key.isEmpty()) {
                messageBuilder.key(key);
            }

            return messageBuilder.sendAsync();

        } catch (Exception e) {
            log.error("Pulsar消息发送失败: topic={}, error={}", topic, e.getMessage(), e);
            throw new RuntimeException("发送消息失败", e);
        }
    }

    public static String getFullTopicByOrgCode(Long orgCode, String namespace, String topic) {
        String tenantName = getTenantName(String.valueOf(orgCode));
        return PulsarClientUtil.getFullTopic(tenantName, namespace, topic);
    }

    public static String getFullTopic(String tenantName, String namespace, String topic) {
        return StrUtil.format("persistent://{}/{}/{}", tenantName, namespace, topic);
    }

    public static String getTenantName(String orgCode) {
        return "tenant-" + orgCode;
    }

    public static String parseTopic(String topic) {
        return StrUtil.format("persistent://{}", topic);
    }


    /**
     * 将对象转换为字节数组
     */
    private static <T> byte[] convertToBytes(T payload) {
        if (payload instanceof String) {
            return ((String) payload).getBytes(StandardCharsets.UTF_8);
        } else if (payload instanceof byte[]) {
            return (byte[]) payload;
        } else {
            return JSONObject.toJSONString(payload).getBytes(StandardCharsets.UTF_8);
        }
    }
}

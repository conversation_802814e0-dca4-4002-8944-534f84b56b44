package com.mingdao.edge.plugin.api.gateway.dto;

import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.plugin.api.gateway.enums.MessageType;
import lombok.*;

import java.io.Serializable;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Data
public class DownMessage<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private String messageId;
    private Integer code;
    private String msg;
    private MessageType downType;
    private T data;

    public DataDto<?> getDataDto() {
        if (this.data instanceof JSONObject) {
            return ((JSONObject) this.data).toJavaObject(DataDto.class);
        } else if (this.data instanceof DataDto) {
            return (DataDto<?>) this.data;
        } else {
            return null;
        }
    }

    public static <L> DownMessage<CommandDto<L>> buildDownMessage(String cmd, L params) {
        return buildDownMessage(cmd, null, params);
    }

    public static <L> DownMessage<CommandDto<L>> buildDownMessage(String cmd, String replyTopic, L params) {
        CommandDto<L> commandDto = new CommandDto<>();
        commandDto.setCommand(cmd);
        commandDto.setData(params);
        commandDto.setReplyTopic(replyTopic);

        DownMessage<CommandDto<L>> downMessage = new DownMessage<>();
        downMessage.setCode(0);
        downMessage.setDownType(MessageType.CMD_GATEWAY_DOWN);
        downMessage.setData(commandDto);

        return downMessage;
    }
}

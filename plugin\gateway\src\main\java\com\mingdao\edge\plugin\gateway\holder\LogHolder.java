package com.mingdao.edge.plugin.gateway.holder;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.core.FileAppender;
import cn.hutool.core.util.StrUtil;
import com.mingdao.edge.core.pulsar.utils.PulsarClientUtil;
import com.mingdao.edge.plugin.api.gateway.dto.UpMessage;
import com.mingdao.edge.plugin.api.gateway.enums.MessageType;
import com.mingdao.edge.plugin.gateway.dto.LogFetchCmd;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.charset.StandardCharsets;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 日志持有者类，负责日志文件的读取、缓存和上传
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
public class LogHolder {
    private static final int DEFAULT_MAX_LINES = 10;
    private static final int MAX_ALLOWED_LINES = 10000;
    private static final int INITIAL_READ_LINES = 10;
    private static final long LOG_REFRESH_INTERVAL = 1L;
    private static final long AUTO_DISPOSE_DELAY = 5L;

    private final int maxLines;
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final String sessionId;
    private final String topic;
    private final String logPath;
    private final LinkedBlockingDeque<String> latestLogs;
    private long lastFilePointer = 0L;

    public LogHolder(String topic, LogFetchCmd logFetchCmd) {
        this.topic = topic;
        this.sessionId = logFetchCmd.getSessionId();
        this.maxLines = validateMaxLines(logFetchCmd.getLines());
        this.latestLogs = new LinkedBlockingDeque<>(maxLines);
        this.logPath = getLogFilePath();

        initializeLogHolder();
    }

    private int validateMaxLines(int requestedLines) {
        return requestedLines < 1 || requestedLines > MAX_ALLOWED_LINES ? DEFAULT_MAX_LINES : requestedLines;
    }

    private void initializeLogHolder() {
        List<String> initLogs = readLastLines(INITIAL_READ_LINES, null);
        latestLogs.addAll(initLogs);

        File file = new File(logPath);
        if (file.exists()) {
            lastFilePointer = file.length();
        }

        startScheduledTasks();
        log.info("日志已初始化，sessionId: {}, topic: {}", sessionId, topic);
    }

    private void startScheduledTasks() {
        scheduler.scheduleAtFixedRate(this::refreshLogs, 0, LOG_REFRESH_INTERVAL, TimeUnit.SECONDS);
        scheduler.scheduleAtFixedRate(this::uploadLogs, 0, LOG_REFRESH_INTERVAL, TimeUnit.SECONDS);
        scheduler.schedule(this::dispose, AUTO_DISPOSE_DELAY, TimeUnit.MINUTES);
    }

    private String getLogFilePath() {
        LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
        Logger rootLogger = context.getLogger("ROOT");
        FileAppender<?> appender = (FileAppender<?>) rootLogger.getAppender("ALL");
        return appender != null ? appender.getFile() : null;
    }

    private void refreshLogs() {
        readNewLines(logPath, lastFilePointer, null);
    }

    private void readNewLines(String filePath, long fromPointer, String keywords) {
        File file = new File(filePath);
        if (!file.exists()) {
            log.warn("日志文件不存在: {}", filePath);
            return;
        }

        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            raf.seek(fromPointer);
            long startPointer = raf.getFilePointer();
            long availableBytes = file.length() - startPointer;

            if (availableBytes > 0) {
                byte[] buffer = new byte[(int) availableBytes];
                raf.readFully(buffer);
                processNewLogContent(buffer, keywords);
                lastFilePointer = raf.getFilePointer();
            }
        } catch (Exception e) {
            log.error("读取日志文件失败: {}", filePath, e);
        }
    }

    private void processNewLogContent(byte[] buffer, String keywords) {
        String content = new String(buffer, StandardCharsets.UTF_8);
        String[] lines = content.split("\r?\n");

        for (String line : lines) {
            if (StrUtil.isNotEmpty(line) && (keywords == null || line.contains(keywords))) {
                latestLogs.offer(line);
            }
        }
    }

    private void uploadLogs() {
        if (latestLogs.isEmpty()) {
            return;
        }

        List<String> logsToUpload = new LinkedList<>();
        latestLogs.drainTo(logsToUpload);

        try {
            UpMessage<List<String>> upMessage = new UpMessage<>(sessionId, MessageType.LOG_FETCH_UP, logsToUpload);
            PulsarClientUtil.sendAsync(topic, upMessage);
        } catch (Exception e) {
            log.error("发送日志异常", e);
        }
    }

    public List<String> readLastLines(int lines, String keywords) {
        LinkedList<String> result = new LinkedList<>();
        File file = new File(logPath);

        if (!file.exists() || !file.isFile()) {
            result.add("日志文件未找到: " + logPath);
            return result;
        }

        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            readLinesFromEnd(raf, lines, keywords, result);
        } catch (Exception e) {
            result.add("读取日志文件错误: " + e.getMessage());
        }

        return result;
    }

    private void readLinesFromEnd(RandomAccessFile raf, int lines, String keywords, LinkedList<String> result)
            throws IOException {
        long fileLength = raf.length();
        long pointer = fileLength - 1;
        int lineCount = 0;
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        while (pointer >= 0 && lineCount < lines) {
            raf.seek(pointer);
            int readByte = raf.readByte();

            if (readByte == '\n') {
                processLineBuffer(baos, keywords, result);
                lineCount++;
            } else if (readByte != '\r') {
                baos.write(readByte);
            }
            pointer--;
        }

        processRemainingLine(baos, keywords, result);
    }

    private void processLineBuffer(ByteArrayOutputStream baos, String keywords, LinkedList<String> result) {
        if (baos.size() > 0) {
            String line = reverseAndDecode(baos.toByteArray());
            if (shouldIncludeLine(line, keywords)) {
                result.addFirst(line);
            }
            baos.reset();
        }
    }

    private void processRemainingLine(ByteArrayOutputStream baos, String keywords, LinkedList<String> result) {
        if (baos.size() > 0) {
            String line = reverseAndDecode(baos.toByteArray());
            if (shouldIncludeLine(line, keywords)) {
                result.addFirst(line);
            }
        }
    }

    private String reverseAndDecode(byte[] bytes) {
        reverseByteArray(bytes);
        return new String(bytes, StandardCharsets.UTF_8);
    }

    private void reverseByteArray(byte[] array) {
        for (int i = 0, j = array.length - 1; i < j; i++, j--) {
            byte tmp = array[i];
            array[i] = array[j];
            array[j] = tmp;
        }
    }

    private boolean shouldIncludeLine(String line, String keywords) {
        return StrUtil.isNotEmpty(line) && (keywords == null || line.contains(keywords));
    }

    public void dispose() {
        try {
            log.info("正在释放日志持有者资源，sessionId: {}", sessionId);
            scheduler.shutdownNow();
            latestLogs.clear();
        } catch (Exception e) {
            log.error("释放日志持有者资源时出错", e);
        }
    }
}

package com.mingdao.edge.plugin.api.gateway;

import com.mingdao.edge.plugin.api.gateway.dto.EdgeState;
import com.mingdao.edge.plugin.api.gateway.enums.StateEnum;

import java.util.Date;
import java.util.List;

public interface EdgeStateService {
    void putState(EdgeState edgeState);
    void putState(StateEnum stateEnum, String bizName, String value, Date time, Boolean editable, Long sysOrg);
    List<Object> getAllStates();
}

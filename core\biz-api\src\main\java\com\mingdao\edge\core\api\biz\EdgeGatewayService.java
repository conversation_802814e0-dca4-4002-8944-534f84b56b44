package com.mingdao.edge.core.api.biz;

import com.alipay.sofa.ark.api.ClientResponse;
import com.alipay.sofa.ark.spi.model.BizState;
import com.mingdao.edge.core.api.biz.dto.BizInfo;

/**
 * 边缘网关相关接口
 * date: 2022/7/14 15:48
 * author: guanjinhua
 * version: 1.0
 */
public interface EdgeGatewayService {

    ClientResponse installBiz(BizInfo bizInfo);

    /**
     * 下载biz包，并安装
     * @param bizName
     * @param bizHttpUrl
     * @param bizVersion
     * @return
     */
    Boolean downloadAndInstallBiz(String bizName, String bizHttpUrl, String bizVersion);

    /**
     * 安装biz
     * @param bizName
     * @param bizId
     * @return
     */
    ClientResponse installBiz(String bizName, String bizUrl, String bizVersion);

    /**
     * 写在bizInfo
     * @param bizInfo
     * @return
     */
    Boolean unInstallBiz(String bizName, String bizVersion);

    ClientResponse healthcheck(String bizName, String bizVersion);
    BizState getBizState(String bizName, String bizVersion);
    /**
     * 判断指定biz是否处于启动中状态
     * @param bizName
     * @param bizVersion
     * @return
     */
    boolean canStart(String bizName, String bizVersion);
}

package com.mingdao.edge.plugin.gateway.config;

import com.mingdao.edge.core.api.biz.dto.BaseEvent;
import com.mingdao.edge.core.api.biz.dto.BizMessage;
import com.mingdao.edge.core.common.disruptor.DisruptorTemplate;
import com.mingdao.edge.core.common.disruptor.handler.DisruptorEventHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * Disruptor配置类
 */
@Slf4j
@Configuration
public class DisruptorConfig {

    @Value("${disruptor.buffer-size:1024}")
    private int bufferSize;

    @Value("${disruptor.consumer-threads:1}")
    private int consumerThreads;

    @Resource
    private DisruptorEventHandler<BizMessage<BaseEvent<?>>> jsonEventHandler;

    @Bean(destroyMethod = "shutdown")
    public DisruptorTemplate<BizMessage<BaseEvent<?>>> disruptorTemplate() {
        return new DisruptorTemplate<>(bufferSize, consumerThreads, jsonEventHandler);
    }
}

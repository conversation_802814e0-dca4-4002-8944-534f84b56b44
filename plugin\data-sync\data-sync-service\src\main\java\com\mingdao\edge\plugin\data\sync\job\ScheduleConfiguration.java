package com.mingdao.edge.plugin.data.sync.job;

import cn.hutool.core.thread.NamedThreadFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import javax.annotation.PreDestroy;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 重写定时任务线程池，改为多线程
 *
 * <AUTHOR>
 */
@Configuration
@EnableScheduling
public class ScheduleConfiguration implements SchedulingConfigurer {

    public static final ScheduledThreadPoolExecutor SCHEDULED_TASK_EXECUTOR = new ScheduledThreadPoolExecutor(Runtime.getRuntime().availableProcessors(),
            new NamedThreadFactory("data-sync-task-", false), new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.setScheduler(SCHEDULED_TASK_EXECUTOR);
    }


    @PreDestroy
    public void stop() {
        SCHEDULED_TASK_EXECUTOR.shutdownNow();
    }

}

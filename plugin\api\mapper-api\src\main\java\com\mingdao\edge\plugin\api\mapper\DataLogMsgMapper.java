package com.mingdao.edge.plugin.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mingdao.edge.plugin.api.mapper.entity.DataLogMsg;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface DataLogMsgMapper extends BaseMapper<DataLogMsg> {

    @Insert("INSERT INTO tb_data_log_msg (log_id, msg, create_time) VALUES (#{logId}, #{msg}, #{createTime})")
    int add(DataLogMsg recordInfo);

    @Delete("DELETE FROM tb_data_log_msg WHERE create_time < '${beforeTime}'")
    int deleteBefore(String beforeTime);

    @Select("select * FROM tb_data_log_msg")
    List<DataLogMsg> selectAll();
}

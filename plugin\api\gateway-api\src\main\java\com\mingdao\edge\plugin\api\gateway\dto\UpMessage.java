package com.mingdao.edge.plugin.api.gateway.dto;

import cn.hutool.core.lang.UUID;
import com.mingdao.edge.plugin.api.gateway.enums.MessageType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpMessage<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    public UpMessage(MessageType upType, T data) {
        this.sessionId = UUID.fastUUID().toString();
        this.upType = upType;
        this.data = data;
    }

    private String sessionId;
    private MessageType upType;
    private T data;
}

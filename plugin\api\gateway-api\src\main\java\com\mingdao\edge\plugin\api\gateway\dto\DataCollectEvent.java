package com.mingdao.edge.plugin.api.gateway.dto;

import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.core.api.biz.dto.BaseEvent;
import com.mingdao.edge.plugin.api.gateway.enums.MessageType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DataCollectEvent extends BaseEvent<Object> {

    /**
     * sample: yuze_moupload
     */
    private String dtName;
    /**
     * mo/part
     */
    private String dataType;
    /**
     * 上传到服务端的topic
     */
    private String topic;
    /**
     * 上传类型
     */
    private MessageType messageType;

    public EventMessage<JSONObject> toEventMessage(JSONObject parsedData) {
        EventMessage<JSONObject> eventMessage = new EventMessage<>();
        eventMessage.setData(parsedData);
        eventMessage.setPublishTopic(this.getTopic());
        eventMessage.setMessageType(this.getMessageType());
        eventMessage.setDtName(this.getDtName());
        eventMessage.setBusinessType(this.getDataType());
        return eventMessage;
    }

    public static DataCollectEvent buildUpEvent(Object id, String dtName, String dataType, String topic, String eventHandlerName) {
        DataCollectEvent dataCollectEvent = new DataCollectEvent();
        dataCollectEvent.setData(id);
        dataCollectEvent.setEventHandlerName(eventHandlerName);
        dataCollectEvent.setDtName(dtName);
        dataCollectEvent.setDataType(dataType);
        dataCollectEvent.setTopic(topic);
        dataCollectEvent.setMessageType(MessageType.DATA_UP);
        return dataCollectEvent;
    }

    public static DataCollectEvent buildPriorityUpEvent(Object id, String dtName, String dataType, String topic, String eventHandlerName) {
        DataCollectEvent dataCollectEvent = new DataCollectEvent();
        dataCollectEvent.setData(id);
        dataCollectEvent.setEventHandlerName(eventHandlerName);
        dataCollectEvent.setDtName(dtName);
        dataCollectEvent.setDataType(dataType);
        dataCollectEvent.setTopic(topic);
        dataCollectEvent.setMessageType(MessageType.DATA_PRIORITY_UP);
        return dataCollectEvent;
    }
}

package com.mingdao.edge.plugin.data.sync.base.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.core.pulsar.utils.PulsarClientUtil;
import com.mingdao.edge.plugin.api.data.sync.constants.DataState;
import com.mingdao.edge.plugin.api.data.sync.constants.MDColumn;
import com.mingdao.edge.plugin.api.data.sync.dto.ColTemp;
import com.mingdao.edge.plugin.api.data.sync.dto.DataUpMessage;
import com.mingdao.edge.plugin.api.gateway.dto.DataDto;
import com.mingdao.edge.plugin.api.gateway.dto.DownMessage;
import com.mingdao.edge.plugin.api.gateway.dto.EventMessage;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Slf4j
public abstract class AbstractDataSyncUp extends AbstractDataSync {

    public AbstractDataSyncUp(String customerName, String moduleName, Integer pageSize) {
        super(customerName, moduleName, pageSize);
    }

    @Override
    public void init(JSONObject taskInfo) {
        super.init(taskInfo);
    }

    @Override
    protected Long convertDataAndSave(JSONObject data, Map<String, ColTemp> fieldMap) {
        Map<String, Object> result = new LinkedHashMap<>();
        for (String targetFieldName : fieldMap.keySet()) {
            // targetFieldName = 易车间字段名称
            ColTemp colTemp = fieldMap.get(targetFieldName);
            String sourceFieldName = colTemp.getDe_name();
            Object sourceValue = data.get(sourceFieldName);
            //// 数字类型时，如果值为空字符串，则设为null
            //if (Boolean.TRUE.equals(colTemp.getIsNumber())
            //        && sourceValue instanceof String
            //        && StrUtil.isBlank((String) sourceValue)) {
            //    sourceValue = null;
            //}
            if (!convertSpecField(targetFieldName, sourceValue, result)) {
                result.put(targetFieldName, sourceValue);
            }
        }

        addSysFields(dtName, result, data);

        return saveData(result);
    }

    @Override
    public void transferData(EventMessage<JSONObject> message) {
        String topic = message.getPublishTopic();
        JSONObject parsedData = message.getData();

        Object id = parsedData.get(MDColumn.ID);
        try {
            DataUpMessage upMessage = new DataUpMessage(message.getMessageType(), this.getDataType(), parsedData);
            PulsarClientUtil.send(topic, upMessage);
            updateState(id, DataState.UPLOAD_SUCCESS);
        } catch (Exception e) {
            updateState(id, DataState.UPLOAD_FAIL);
            addLogMsg((Long) id, e.getMessage());
        }
    }

    @Override
    public void transferDataCallback(DownMessage<?> downMessage) {
        DataDto<?> data1 = downMessage.getDataDto();
        String id = data1.getId();
        if (downMessage.getCode() == 0) {
            updateState(id, DataState.SYNC_SUCCESS);
        } else if (downMessage.getCode() == 2) {
            updateState(id, DataState.DATA_ERROR);
            addLogMsg(Long.parseLong(id), downMessage.getMsg());
        } else {
            updateState(id, DataState.SYNC_FAIL);
            addLogMsg(Long.parseLong(id), downMessage.getMsg());
        }
    }

}

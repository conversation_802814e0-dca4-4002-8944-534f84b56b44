package com.mingdao.edge.plugin.api.data.sync.dto;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
public class ColTemp {
    private String de_fixed;
    /**
     * input
     */
    private String de_type;
    private Integer dp_manage;
    /**
     * 0
     */
    private Integer de_group;
    private Integer de_expfiled;
    /**
     * 钰泽
     */
    private String dm_title;
    private Integer de_searchable;
    private String dp_hcode;
    private Integer de_modchange;
    private String dp_width;
    private Integer de_necessary;
    private String de_placeholder;
    private String dp_height;
    private Integer de_disabled;
    /**
     * 关键字、原始需求数
     */
    private String de_title;
    /**
     * 100
     */
    private Integer de_order;
    /**
     * number
     */
    private String de_df_code;
    private Integer de_sum;
    /**
     * 关键字
     */
    private String sys_title;
    private String de_t_uid;
    /**
     * pages_list
     */
    private String dp_type;
    /**
     * yuze_moupload
     */
    private String de_dp_code;
    /**
     *  QTYFORPRODUCTREQUEST
     */
    private String de_reference;
    private Integer de_custom;
    /**
     * yuze_moupload
     */
    private String dp_code;
    private String dp_dm_code;
    private Integer de_quick;
    /**
     * 数据传输-钰泽-订单上传
     */
    private String dp_title;
    /**
     * dm_code = yuze
     */
    private String dm_code;
    /**
     * 1
     */
    private Integer de_sortable;
    /**
     * keyword、qtyforproductrequest
     */
    private String de_name;
    /**
     * input
     */
    private String org_type;

    private String columnName;
    public String getColumnName() {
        if (this.columnName == null) {
            String columnName = this.getDe_reference();
            if (StrUtil.isEmpty(columnName)) {
                columnName = this.getDe_name();
            }
            this.columnName = columnName;
        }
        return this.columnName;
    }
    private Boolean isNumber;
    public Boolean getIsNumber() {
        if (this.isNumber == null) {
            String columnType = this.getDe_df_code();
            this.isNumber = "Double".equalsIgnoreCase(columnType) || "Integer".equalsIgnoreCase(columnType);
        }
        return this.isNumber;
    }

    public Object convertFieldType(Object paramValue) {
        Object value = paramValue;
        if (this.getIsNumber() && value instanceof String) {
            if (ObjectUtil.isEmpty(value)) {
                value = null;
            } else {
                if ("Double".equalsIgnoreCase(this.getDe_df_code())) {
                    value = Double.parseDouble((String) paramValue);
                } else if ("Integer".equalsIgnoreCase(this.getDe_df_code())) {
                    value = Integer.parseInt((String) paramValue);
                }
            }
        }
        return value;
    }
}

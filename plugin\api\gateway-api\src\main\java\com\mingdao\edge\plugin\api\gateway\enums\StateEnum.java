package com.mingdao.edge.plugin.api.gateway.enums;

import com.mingdao.edge.plugin.api.gateway.constants.StateName;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum StateEnum {
    DATA_LAST_QUERY_TIME(StateName.DATA_LAST_QUERY_TIME, "DATA_LAST_QUERY_TIME"),
    EDGE_START_TIME(StateName.EDGE_START_TIME, "EDGE_START_TIME"),
    EDGE_BIZ_STATE_TIME(StateName.EDGE_BIZ_STATE_TIME, "EDGE_BIZ_STATE_TIME"),

    ;

    @Getter
    private final String stateName;
    @Getter
    private final String stateKey;
}

package com.mingdao.edge.core.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Arrays;

/**
 * description ReflectUtils
 *
 * <AUTHOR>
 * @since 2022/7/11 19:08
 */
@Slf4j
public class ReflectUtils {
    public static <T> Class<T> getSuperClassGenericType(Class<?> clazz, int index) {
        Type genType = clazz.getGenericSuperclass();
        if (!(genType instanceof ParameterizedType)) {
            log.warn(String.format("Warn: %s's superclass not ParameterizedType", clazz.getSimpleName()));
            return (Class<T>) Object.class;
        } else {
            Type[] params = ((ParameterizedType)genType).getActualTypeArguments();
            if (index < params.length && index >= 0) {
                if (!(params[index] instanceof Class)) {
                    log.warn(String.format("Warn: %s not set the actual class on superclass generic parameter", clazz.getSimpleName()));
                    return (Class<T>) Object.class;
                } else {
                    return (Class)params[index];
                }
            } else {
                log.warn(String.format("Warn: Index: %s, Size of %s's Parameterized Type: %s .", index, clazz.getSimpleName(), params.length));
                return (Class<T>) Object.class;
            }
        }
    }

    /**
     * 获取接口泛型参数的类型
     *
     * @param clazz 对象类型
     * @param index 泛型所在下标位
     * @return Class (如无法找到, 返回Object.class)
     */
    @SuppressWarnings("unchecked")
    public static <T> Class<T> getInterfaceGenericType(final Class<?> clazz, final Class<?> interfaceClazz, final int index) {
        Class<T> defaultClazz = (Class<T>) Object.class;
        if (null == clazz || null == interfaceClazz || index < 0) {
            return defaultClazz;
        }
        Type[] genericInterfaces = clazz.getGenericInterfaces();
        if (genericInterfaces.length == 0) {
            return defaultClazz;
        }
        //TODO 递归嵌套
        return Arrays.stream(genericInterfaces)
                .filter(type -> type instanceof ParameterizedType)
                .map(type -> (ParameterizedType) type)
                .filter(type -> interfaceClazz.isAssignableFrom((Class<?>) type.getRawType()))
                .findFirst()
                .map(ParameterizedType::getActualTypeArguments)
                .map(typeArgs -> {
                    int typeArgsLen = typeArgs.length;

                    if (index >= typeArgsLen) {
                        log.warn(
                                "{}类上的{}接口类的泛型参数只有{}个，传入的index参数值{}有误！",
                                clazz.getCanonicalName(),
                                interfaceClazz.getCanonicalName(),
                                typeArgsLen,
                                index);
                        return defaultClazz;
                    }
                    if (!(typeArgs[index] instanceof Class)) {
                        log.warn(
                                "{}类上的{}接口类的{}下标的泛型参数没有设置实际的泛型类型！",
                                clazz.getCanonicalName(),
                                interfaceClazz.getCanonicalName(),
                                index);
                        return defaultClazz;
                    }
                    return (Class<T>) typeArgs[index];
                })
                .orElse(defaultClazz);
    }
}

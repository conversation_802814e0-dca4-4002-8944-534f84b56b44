package com.mingdao.edge.plugin.gateway.handler.down.event;

import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.util.StrUtil;
import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.mingdao.edge.core.common.exception.Assert;
import com.mingdao.edge.plugin.api.gateway.GatewayDownHandler;
import com.mingdao.edge.plugin.api.gateway.constants.GatewayConstants;
import com.mingdao.edge.plugin.api.gateway.dto.CommandDto;
import com.mingdao.edge.plugin.gateway.dto.LogFetchCmd;
import com.mingdao.edge.plugin.gateway.holder.LogHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@Service
@SofaService(uniqueId = GatewayConstants.GATEWAY_CMD_BEAN_PREFIX + "log_fetch")
public class LogFetchHandler implements GatewayDownHandler {

    private static final ConcurrentHashSet<LogHolder> logHolders = new ConcurrentHashSet<>(1);
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    @Async
    @Override
    public Object process(String messageId, CommandDto<?> commandDto) {
        try {
            Assert.notNull(commandDto, "参数不能为空");

            LogFetchCmd logFetchCmd = commandDto.toJSONObject().toJavaObject(LogFetchCmd.class);

            Assert.notNull(logFetchCmd, "参数不能为空");

            if (Boolean.TRUE.equals(logFetchCmd.getFlag())) {
                if (logHolders.isEmpty()) {
                    LogHolder logHolder = new LogHolder(commandDto.getReplyTopic(), logFetchCmd);
                    // 5分钟后自动执行dispose方法
                    scheduler.schedule(() -> {
                        if (logHolder != null) {
                            logHolder.dispose();
                            logHolders.remove(logHolder);
                        }
                    }, 5, TimeUnit.MINUTES);
                    logHolders.add(logHolder);
                } else {
                    log.warn("已有日志上传");
                }
            } else {
                if (!logHolders.isEmpty()) {
                    logHolders.stream().findFirst().ifPresent(LogHolder::dispose);
                    logHolders.clear();
                }
            }


        } catch (Exception e) {
            log.error(StrUtil.format("执行指令异常 {} {}", "data_priority_upload", e.getMessage()), e);
        }
        return null;
    }
}

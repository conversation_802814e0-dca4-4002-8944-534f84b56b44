package com.mingdao.edge.plugin.gateway.service.impl;

import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.alipay.sofa.runtime.api.annotation.SofaServiceBinding;
import com.mingdao.edge.core.common.disruptor.DisruptorTemplate;
import com.mingdao.edge.plugin.api.gateway.DisruptorApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-16
 */

@Slf4j
@Service
@RequiredArgsConstructor
@SofaService(bindings = {@SofaServiceBinding(serialize = true)})
public class DisruptorApiImpl implements DisruptorApi {

    @Resource
    private DisruptorTemplate disruptorTemplate;

    @Override
    public <T> void publish(T data) {
        disruptorTemplate.publish(data);
    }
}

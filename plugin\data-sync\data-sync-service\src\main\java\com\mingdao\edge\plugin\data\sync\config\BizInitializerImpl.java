package com.mingdao.edge.plugin.data.sync.config;

import com.alipay.sofa.runtime.api.annotation.SofaReference;
import com.mingdao.edge.core.api.biz.BizCache;
import com.mingdao.edge.core.api.biz.BizInitializer;
import com.mingdao.edge.core.common.context.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * description BizInitializerImpl
 *
 * <AUTHOR>
 * @since 2022/7/12 17:49
 */
@Slf4j
@Component
@Order(9999)
public class BizInitializerImpl implements BizInitializer {

    @Value("${app.version}")
    private String appVersion;

    @SofaReference
    private BizCache bizCache;

    @PostConstruct
    @Override
    public void init() {
        String applicationName = SpringContextUtils.getApplicationName();
        log.info("[DEBUG]开始初始化{}", applicationName);
        Object cacheObject = bizCache.getInitParams(applicationName, appVersion, BizCache.kEY_INIT_PARAMS);
        log.info("[DEBUG]完成初始化{}", applicationName);
    }

}

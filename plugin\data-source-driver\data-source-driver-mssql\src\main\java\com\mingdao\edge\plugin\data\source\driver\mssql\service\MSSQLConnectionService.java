package com.mingdao.edge.plugin.data.source.driver.mssql.service;

import com.mingdao.edge.plugin.data.source.driver.mssql.config.DatabaseConnectionManager;
import com.mingdao.edge.plugin.data.source.driver.api.dto.MSSQLProperty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class MSSQLConnectionService {

    private final DatabaseConnectionManager connectionManager;

    /**
     * 初始化数据库连接
     * @param configs 数据库配置列表
     */
    public void initializeDatabaseConnections(List<MSSQLProperty> configs) {
        connectionManager.initializeConnections(configs);
    }

    /**
     * 通过数据库名称获取数据源
     * @param databaseName 数据库名称
     * @return 数据源
     */
    public DataSource getDataSource(String databaseName) {
        return connectionManager.getDataSource(databaseName);
    }

    /**
     * 获取指定数据库的数据源（兼容旧版本）
     * @param host 主机地址（忽略）
     * @param databaseName 数据库名称
     * @return 数据源
     * @deprecated 使用 getDataSource(String databaseName) 替代
     */
    @Deprecated
    public DataSource getDataSource(String host, String databaseName) {
        return getDataSource(databaseName);
    }
}

package com.mingdao.edge.plugin.data.source.driver.mssql.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.alipay.sofa.runtime.api.annotation.SofaServiceBinding;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mingdao.edge.plugin.data.source.driver.api.dto.BasePageQueryDto;
import com.mingdao.edge.plugin.data.source.driver.api.dto.DbPageQueryDto;
import com.mingdao.edge.plugin.data.source.driver.api.exception.ApiException;
import com.mingdao.edge.plugin.data.source.driver.api.service.DataCollector;
import com.mingdao.edge.plugin.data.source.driver.mssql.service.MSSQLMyBatisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
@Service
@SofaService(bindings = {@SofaServiceBinding(serialize = true)}, uniqueId = "DataCollector_${app.version}")
public class DataCollectorImpl implements DataCollector {

    @Autowired
    private MSSQLMyBatisService mssqlMyBatisService;

    @Override
    public JSONObject getPageData(BasePageQueryDto basePageQueryDto) {
        DbPageQueryDto dbPageQueryDto = (DbPageQueryDto) basePageQueryDto;

        String sql = dbPageQueryDto.getSql();
        JSONObject params = dbPageQueryDto.getParams();
        Integer page = dbPageQueryDto.getPage();
        Integer size = dbPageQueryDto.getSize();

        IPage<Map<String, Object>> pageResult = mssqlMyBatisService.selectPageBySql(sql, page, size, params);


        //
        //String url = apiPageQueryDto.getUrl();
        //JSONObject params = apiPageQueryDto.getParams();
        //JSONObject headers = apiPageQueryDto.getHeader();
        //
        //HttpHeaders httpHeaders = new HttpHeaders();
        //httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        //for (String key : headers.keySet()) {
        //    httpHeaders.add(key, headers.getString(key));
        //}
        JSONObject result = (JSONObject) JSON.toJSON(pageResult);
        return result;
    }

    @Override
    public JSONObject getOneData(BasePageQueryDto basePageQueryDto) throws ApiException {
        DbPageQueryDto dbPageQueryDto = (DbPageQueryDto) basePageQueryDto;

        String sql = dbPageQueryDto.getSql();
        JSONObject params = dbPageQueryDto.getParams();
        Map<String, Object> result = mssqlMyBatisService.selectOne(sql, params);

        return new JSONObject(result);
    }
}

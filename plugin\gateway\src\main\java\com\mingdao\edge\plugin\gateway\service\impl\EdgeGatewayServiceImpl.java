package com.mingdao.edge.plugin.gateway.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alipay.sofa.ark.api.ArkClient;
import com.alipay.sofa.ark.api.ClientResponse;
import com.alipay.sofa.ark.api.ResponseCode;
import com.alipay.sofa.ark.spi.constant.Constants;
import com.alipay.sofa.ark.spi.model.BizOperation;
import com.alipay.sofa.ark.spi.model.BizState;
import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.mingdao.edge.core.api.biz.EdgeGatewayService;
import com.mingdao.edge.core.api.biz.dto.BizInfo;
import com.mingdao.edge.plugin.gateway.util.BizFileUtil;
import com.mingdao.edge.plugin.gateway.util.BizHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Optional;

/**
 * description EdgeGatewayServiceImpl
 *
 * <AUTHOR>
 * @since 2022/7/14 15:51
 */
@Slf4j
@Service
@SofaService
public class EdgeGatewayServiceImpl implements EdgeGatewayService {

    @Override
    public ClientResponse installBiz(BizInfo bizInfo) {
        return installBiz(bizInfo.getBizName(), bizInfo.getVersion(), bizInfo.getLocalPath());
    }

    @Override
    public Boolean downloadAndInstallBiz(String bizHttpUrl, String bizName, String bizVersion) {

        // 下载biz获取biz包的路径
        Optional<String> bizPathOptional = BizHttpUtil.downLoad(bizHttpUrl, BizFileUtil.getDownLoadPath(bizName, bizVersion));

        // 如果文件下载失败，就返回安装失败
        if (!bizPathOptional.isPresent()) {
            return false;
        }

        String bizPath = bizPathOptional.get();
        installBiz(bizName, bizVersion, bizPath);

        // 安装完之后把下载的文件删除

        return true;
    }

    @Override
    public ClientResponse installBiz(String bizName, String bizVersion, String bizUrl) {
        BizOperation bizOperation = new BizOperation();
        bizOperation.setBizName(bizName);
        bizOperation.setBizVersion(bizVersion);
        bizOperation.setParameters(Collections.singletonMap(Constants.CONFIG_BIZ_URL, "file:///" + bizUrl));
        bizOperation.setOperationType(BizOperation.OperationType.INSTALL);
        try {
            log.info("安装 {} {}", bizName, bizVersion);
            return ArkClient.installOperation(bizOperation);

            // 安装完之后删除在下载目录的文件
            // 如果不是debug模式就将文件删除
//            if (!isDebug) {
//                BizFileUtil.deleteDownLoadBiz(bizName, bizVersion);
//            }
        } catch (Throwable e) {
            String errMsg = StrUtil.format("安装 {} biz 失败，version = {}", bizName, bizVersion);
            log.error(errMsg, e);
            return new ClientResponse().setCode(ResponseCode.FAILED).setMessage(errMsg);
        }
    }

    @Override
    public Boolean unInstallBiz(String bizName, String bizVersion) {
        try {
            ClientResponse clientResponse = ArkClient.checkBiz(bizName);
            if (CollUtil.isNotEmpty(clientResponse.getBizInfos())) {
                log.info("开始卸载biz：{}", bizName);
                ArkClient.uninstallBiz(bizName, bizVersion);
                log.info("卸载biz成功：{}", bizName);

                // 卸载完之后把旧的文件夹删除
                BizFileUtil.deleteUninstallBizDir(bizName, bizVersion);
            }
        } catch (Throwable e) {
            log.error("卸载 {} biz 失败", bizName, e);
        }
        return true;
    }

    public ClientResponse healthcheck(String bizName, String bizVersion) {
        return ArkClient.checkBiz(bizName, bizVersion);
    }

    public BizState getBizState(String bizName, String bizVersion) {
        return ArkClient.getBizManagerService().getBizState(bizName, bizVersion);
    }

    @Override
    public boolean canStart(String bizName, String bizVersion) {
        BizState bizState = getBizState(bizName, bizVersion);
        log.info("bizState {} {} {}", bizName, bizVersion, bizState);
        return !(BizState.RESOLVED.equals(bizState)
                || BizState.DEACTIVATED.equals(bizState)
                || BizState.ACTIVATED.equals(bizState));
    }
}

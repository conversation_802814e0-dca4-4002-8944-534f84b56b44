package com.mingdao.edge.plugin.gateway.handler;

import cn.hutool.core.lang.id.NanoId;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.core.common.context.SpringContextUtils;
import com.mingdao.edge.core.pulsar.utils.PulsarClientUtil;
import com.mingdao.edge.plugin.api.gateway.GatewayDownHandler;
import com.mingdao.edge.plugin.api.gateway.constants.GatewayConstants;
import com.mingdao.edge.plugin.api.gateway.dto.CommandDto;
import com.mingdao.edge.plugin.api.gateway.dto.DownMessage;
import com.mingdao.edge.plugin.api.gateway.enums.MessageType;
import com.mingdao.edge.plugin.gateway.holder.GatewayDownHandlerHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.*;

import java.nio.charset.StandardCharsets;

/**
 * 抽象Pulsar消息监听器
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Slf4j
public abstract class AbstractNamespaceMessageListener implements MessageListener<byte[]> {

    private final GatewayDownHandlerHolder gatewayDownHandlerHolder;

    public AbstractNamespaceMessageListener(String tenantName, String namespace, String topic) {
        this.gatewayDownHandlerHolder = SpringContextUtils.getBean(GatewayDownHandlerHolder.class);
        try {
            log.info("初始化订阅：{}-{}-{}", tenantName, namespace, topic);
            String subscriptionName = StrUtil.format("{}-{}-{}", tenantName, namespace, NanoId.randomNanoId(4));
            PulsarClientUtil.subscribeNamespaceAllTopic(tenantName, namespace, subscriptionName, SubscriptionType.Shared, this);
        } catch (PulsarClientException e) {
            String errorMsg = StrUtil.format("{}-{}-{}：{}", tenantName, namespace, topic, e.getMessage());
            log.error("创建订阅失败: " + errorMsg);
        }
    }


    @Override
    public void received(Consumer<byte[]> consumer, Message<byte[]> message) {
        try {
            // 获取消息内容
            String messageContent = new String(message.getData(), StandardCharsets.UTF_8);

            // 获取消息属性
            String messageId = message.getMessageId().toString();
            String topic = message.getTopicName();
            String key = message.getKey();
            long publishTime = message.getPublishTime();

            log.info("接收到Pulsar消息 {} - 主题: {}, 消息ID: {}, Key: {}, 发布时间: {}, 内容: {}",
                    this.getClass().getSimpleName(),
                    topic, messageId, key, publishTime, messageContent);

            // 打印消息属性
            if (message.getProperties() != null && !message.getProperties().isEmpty()) {
                log.info("消息属性: {}", message.getProperties());
            }


            DownMessage<?> downMessage = JSON.parseObject(messageContent, DownMessage.class);
            if (downMessage == null) {
                log.warn("[网关下行消息]异常：缺少实体内容");
                return;
            }

            MessageType downType = downMessage.getDownType();
            if (downType != null) {
                if (MessageType.CMD_GATEWAY_DOWN.equals(downType)) {
                    CommandDto<?> commandDto;
                    Object downMessageData = downMessage.getData();
                    if (downMessageData instanceof JSONObject) {
                        commandDto = ((JSONObject) downMessageData).toJavaObject(CommandDto.class);
                    } else if (downMessageData instanceof CommandDto) {
                        commandDto = (CommandDto<?>) downMessageData;
                    } else {
                        commandDto = JSON.parseObject(JSON.toJSONString(downMessageData), CommandDto.class);
                    }

                    //String downHandler = downMessage.getDownHandler();
                    if (commandDto != null) {
                        String command = commandDto.getCommand();
                        String beanName = GatewayConstants.GATEWAY_CMD_BEAN_PREFIX + command;
                        GatewayDownHandler gatewayDownHandler = gatewayDownHandlerHolder.getService(beanName, GatewayDownHandler.class);
                        gatewayDownHandler.process(downMessage.getMessageId(), commandDto);
                    }
                }
            }



        } catch (Exception e) {
            log.error("[网关下行消息]异常: " + e.getMessage(), e);
        } finally {
            try {
                consumer.acknowledge(message);
            } catch (PulsarClientException e) {
                log.error("发送ack失败" + e.getMessage());
            }
        }
    }
}

package com.mingdao.edge.plugin.api.data.sync.dto;

import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.plugin.api.gateway.dto.UpMessage;
import com.mingdao.edge.plugin.api.gateway.enums.MessageType;
import lombok.Data;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Data
public class DataUpMessage extends UpMessage<JSONObject> {
    private String dataType;

    public DataUpMessage(MessageType upType, String dataType, JSONObject data) {
        super(upType, data);
        this.dataType = dataType;
    }

}

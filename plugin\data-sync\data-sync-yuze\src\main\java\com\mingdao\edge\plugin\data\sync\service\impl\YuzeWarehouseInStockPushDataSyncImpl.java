package com.mingdao.edge.plugin.data.sync.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.mingdao.edge.core.common.dto.BaseResponse;
import com.mingdao.edge.plugin.api.data.sync.dto.WarehouseProject;
import com.mingdao.edge.plugin.api.data.sync.service.DataSyncService;
import com.mingdao.edge.plugin.data.source.driver.api.exception.ApiException;
import com.mingdao.edge.plugin.data.sync.base.constants.DataSyncConstants;
import com.mingdao.edge.plugin.data.sync.base.utils.DownloadCloudTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 入库记录反写
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@Service
@SofaService(uniqueId = DataSyncConstants.DATA_SYNC_SERVICE + "_yuze_warehouseinstockpush")
public class YuzeWarehouseInStockPushDataSyncImpl extends AbstractYuzeDataSyncDown implements DataSyncService {

    public YuzeWarehouseInStockPushDataSyncImpl() {
        super("yuze", "warehouseinstockpush", 1000);
    }

    @Override
    public JSONArray getPushDataList(WarehouseProject warehouseproject, Integer page, Integer limit) {
        String queryParams = warehouseproject.getQueryString();
        JSONObject params = DownloadCloudTools.urlParamsToJSONObject(queryParams);
        params.put("page", page);
        params.put("limit", limit);
        BaseResponse<JSONArray> response = ewsApi.searchWarehouseBaseList(params);
        if (response != null) {
            return response.getData();
        }
        return null;
    }

    @Override
    public void setUpdateObjectSuccess(String warehouseProjectId, Long dataId, JSONObject updateObj, JSONObject dataObj, JSONObject result) {
        String autoId = dataObj.getString("autoid");
        updateObj.put("AUTOID", autoId);
        if (!StrUtil.isBlank(autoId)) {
            String msg = result.getString("msg");

            updateObj.put("TRANSFERCODE", result.getString("data"));
            updateObj.put("MSG", msg);

            updateObj.put("STATUS", 2);
            Integer transfercount = (dataObj.containsKey("TRANSFERCOUNT") ? dataObj.getInteger("TRANSFERCOUNT") : 0) + 1;
            updateObj.put("TRANSFERCOUNT", transfercount);
            updateObj.put("WAREHOUSEPROJECT", warehouseProjectId);

            addLogMsg(dataId, "同步成功");
        }
    }

    @Override
    protected void setUpdateObjectFail(String warehouseProjectId, Long dataId, JSONObject updateObj, String msg) {
        updateObj.put("STATUS", 3);
        updateObj.put("TRANSFERCODE", "");
        updateObj.put("MSG", msg);

        addLogMsg(dataId, "同步失败");
    }

    @Override
    public JSONObject getData(JSONObject dataObj) throws ApiException {
        JSONObject result = new JSONObject();
        String autoId = dataObj.getString("AUTOID");
        String moCode = dataObj.getString("MOCODE");
        if (!StrUtil.isBlank(autoId) && !StrUtil.isBlank(moCode)) {
            return yuzeApi.getMmMes(autoId, moCode);
        }
        result.put("code", 500);
        return result;
    }

    @Override
    protected JSONObject addData(JSONObject dataObj) throws ApiException {
        return yuzeApi.saveMmMes(dataObj);
    }

    @Override
    protected JSONObject editData(JSONObject dataObj) throws ApiException {
        return yuzeApi.editMmMes(dataObj);
    }

    @Override
    public boolean convertSpecField(String fieldName, Object value, Map<String, Object> result) {
        if ("sysCreateDate".equalsIgnoreCase(fieldName)) {
            if (value instanceof String) {
                Date date = DateUtil.parse((String) value);
                result.put(fieldName, DateUtil.format(date, DatePattern.UTC_SIMPLE_PATTERN));
                return true;
            }
        }
        return false;
    }

    @Override
    protected void updateCloudStatus(JSONObject updateObj) {
        ewsApi.updateWarehouseDownloadStatus(updateObj);
    }
}

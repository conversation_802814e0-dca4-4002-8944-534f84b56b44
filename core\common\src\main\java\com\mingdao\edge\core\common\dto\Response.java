package com.mingdao.edge.core.common.dto;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mingdao.edge.core.common.exception.ApplicationException;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.Date;
import java.util.Optional;

/**
 * 响应报文
 */
@Data
@NoArgsConstructor
public class Response<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 响应状态码
     */
    private int code = HttpResponseStatus.SUCCESS.code();

    /**
     * 结果描述
     */
    private String msg = HttpResponseStatus.SUCCESS.msg();

    /**
     * 数据结果集
     */
    private T data;

    /**
     * 时间戳
     */
    private Date timestamp = new Date();

    /**
     * @param status 响应状态
     */
    public Response(IResponseStatus status) {
        this(
                status,
                null,
                null
        );
    }

    /**
     * @param status 响应状态
     * @param msg    结果描述
     */
    public Response(IResponseStatus status, String msg) {
        this(
                status,
                null,
                msg
        );
    }

    /**
     * @param status 响应状态
     * @param data   数据结果集
     */
    public Response(IResponseStatus status, T data) {
        this(
                status,
                data,
                Optional.ofNullable(status).map(IResponseStatus::msg).orElse(null)
        );
    }

    /**
     * @param status 响应状态
     * @param data   数据结果集
     * @param msg    结果描述
     */
    public Response(IResponseStatus status, T data, String msg) {
        this(
                Optional.ofNullable(status).map(IResponseStatus::code).orElse(HttpResponseStatus.SUCCESS.code()),
                data,
                msg
        );
    }

    /**
     * @param code 响应状态码
     * @param data 数据结果集
     * @param msg  结果描述
     */
    public Response(int code, T data, String msg) {
        this.code = code;
        this.data = data;
        this.msg = msg;
    }

    /**
     * 断言调用成功
     *
     * @param response 响应信息体
     */
    public static void assertSuccess(Response<?> response) {
        Assert.notNull(response, "接口返回结果为空");
        Assert.isTrue(response.isSuccess(), String.format("接口调用失败, code:%s, msg:%s", response.getCode(), response.getMsg()));
    }

    /**
     * 响应信息体解包，获取数据集
     *
     * @param response 响应信息体
     * @return 数据集
     */
    public static <T> T unpack(Response<T> response) {
        return unpack(response, null);
    }

    /**
     * 响应信息体解包，获取数据集
     *
     * @param response    响应信息体
     * @param defaultData 默认数据集
     * @return 数据集
     */
    public static <T> T unpack(Response<T> response, T defaultData) {
        assertSuccess(response);
        return ObjectUtil.defaultIfNull(response.getData(), defaultData);
    }

    /**
     * 构建响应体-成功
     *
     * @return 响应体-成功
     */
    public static <T> Response<T> success() {
        return success(null);
    }

    /**
     * 构建响应体-成功
     *
     * @param data 数据结果集对象
     * @return 响应体-成功
     */
    public static <T> Response<T> success(T data) {
        return success(HttpResponseStatus.SUCCESS.msg(), data);
    }

    /**
     * 构建响应体-成功
     *
     * @param msg  结果描述
     * @param data 数据结果集对象
     * @return 响应体-成功
     */
    public static <T> Response<T> success(String msg, T data) {
        return new Response<>(HttpResponseStatus.SUCCESS, data, msg);
    }

    /**
     * 构建响应体-失败
     *
     * @return 响应体-失败
     */
    public static <T> Response<T> error() {
        return new Response<>(HttpResponseStatus.ERROR, null, HttpResponseStatus.ERROR.msg());
    }

    /**
     * 构建响应体-失败
     *
     * @param msg 结果描述
     * @return 响应体-失败
     */
    public static <T> Response<T> error(String msg) {
        return new Response<>(HttpResponseStatus.ERROR, null, msg);
    }

    /**
     * 判断接口调用是否成功
     *
     * @return 成功返回true
     */
    @JsonIgnore
    public boolean isSuccess() {
        return HttpResponseStatus.SUCCESS.code() == code;
    }
}

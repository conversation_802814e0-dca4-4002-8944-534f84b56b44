package com.mingdao.edge.plugin.data.source.driver.mssql.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mingdao.edge.plugin.data.source.driver.mssql.holder.MSSQLPropertyHolder;
import com.mingdao.edge.plugin.data.source.driver.mssql.mapper.DynamicMssqlMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MSSQL MyBatis服务实现类
 * 类似gateway模块的DataBaseServiceImpl，使用MyBatis执行动态SQL
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Service
public class MSSQLMyBatisService {

    @Autowired
    private DynamicMssqlMapper dynamicMssqlMapper;

    /**
     * 检查是否已初始化
     * @return true if initialized, false otherwise
     */
    private boolean isInitialized() {
        if (!MSSQLPropertyHolder.isInitialized()) {
            log.error("MSSQLMyBatisService not initialized. Database configuration not found.");
            return false;
        }
        return true;
    }

    /**
     * 执行查询SQL
     * @param sql SQL语句（使用MyBatis参数格式，如：SELECT * FROM table WHERE name = #{params.name}）
     * @param params SQL参数
     * @return 查询结果列表
     */
    public List<Map<String, Object>> select(String sql, Map<String, Object> params) {
        if (!isInitialized()) {
            return new java.util.ArrayList<>();
        }

        validateSql(sql);
        params = validateParams(params);

        try {
            List<Map<String, Object>> result = dynamicMssqlMapper.selectBySql(sql, params);
            log.debug("执行查询SQL成功，返回{}条记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("执行查询SQL失败，SQL: {}, 参数: {}", sql, params, e);
            throw new RuntimeException("执行查询SQL失败: " + e.getMessage());
        }
    }

    /**
     * 执行分页查询SQL
     * @param sql SQL语句（使用MyBatis参数格式）
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页大小
     * @param params SQL参数
     * @return 分页结果
     */
    public IPage<Map<String, Object>> selectPageBySql(String sql, Integer pageNum, Integer pageSize, Map<String, Object> params) {
        if (!isInitialized()) {
            return new Page<>();
        }

        validateSql(sql);
        params = validateParams(params);

        // 参数校验
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }

        try {
            // 手动实现分页 - 避免MyBatis Plus分页插件问题

            // 1. 先查询总记录数
            long total = count(sql, params);

            // 2. 构建SQL Server 2008兼容的分页SQL
            String pageSql = buildPageSql(sql, pageNum, pageSize);

            // 3. 执行分页查询
            List<Map<String, Object>> records = dynamicMssqlMapper.selectBySql(pageSql, params);

            // 4. 构建分页结果对象
            Page<Map<String, Object>> page = new Page<>(pageNum, pageSize);
            page.setTotal(total);
            page.setRecords(records);

            log.debug("执行手动分页查询SQL成功，总记录数：{}，当前页数据量：{}",
                    page.getTotal(), page.getRecords().size());
            return page;
        } catch (Exception e) {
            log.error("执行分页查询SQL失败，SQL: {}, 参数: {}", sql, params, e);
            throw new RuntimeException("执行分页查询SQL失败: " + e.getMessage());
        }
    }

    /**
     * 执行插入SQL
     * @param sql SQL语句（使用MyBatis参数格式）
     * @param params SQL参数
     * @return 生成的主键ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long insert(String sql, Map<String, Object> params) {
        if (!isInitialized()) {
            return null;
        }

        validateSql(sql);
        params = validateParams(params);

        try {
            // 检查SQL是否为INSERT语句
            String normalizedSql = sql.trim().toUpperCase();
            if (!normalizedSql.startsWith("INSERT INTO")) {
                throw new IllegalArgumentException("SQL语句必须是INSERT语句");
            }

            // 确保params中有id字段
            if (!params.containsKey("id")) {
                params.put("id", null);
            }

            int rows = dynamicMssqlMapper.insertBySql(sql, params);
            log.debug("执行插入SQL成功，影响{}行", rows);

            // 获取生成的主键ID
            Long generatedId = (Long) params.get("id");
            log.debug("生成的主键ID: {}", generatedId);
            return generatedId;
        } catch (Exception e) {
            log.error("执行插入SQL失败，SQL: {}, 参数: {}", sql, params, e);
            throw new RuntimeException("执行插入SQL失败: " + e.getMessage());
        }
    }

    /**
     * 执行更新SQL
     * @param sql SQL语句（使用MyBatis参数格式）
     * @param params SQL参数
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int update(String sql, Map<String, Object> params) {
        if (!isInitialized()) {
            return 0;
        }

        validateSql(sql);
        params = validateParams(params);

        try {
            int rows = dynamicMssqlMapper.updateBySql(sql, params);
            log.debug("执行更新SQL成功，影响{}行", rows);
            return rows;
        } catch (Exception e) {
            log.error("执行更新SQL失败，SQL: {}, 参数: {}", sql, params, e);
            throw new RuntimeException("执行更新SQL失败: " + e.getMessage());
        }
    }

    /**
     * 执行删除SQL
     * @param sql SQL语句（使用MyBatis参数格式）
     * @param params SQL参数
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int delete(String sql, Map<String, Object> params) {
        if (!isInitialized()) {
            return 0;
        }

        validateSql(sql);
        params = validateParams(params);

        try {
            int rows = dynamicMssqlMapper.deleteBySql(sql, params);
            log.debug("执行删除SQL成功，影响{}行", rows);
            return rows;
        } catch (Exception e) {
            log.error("执行删除SQL失败，SQL: {}, 参数: {}", sql, params, e);
            throw new RuntimeException("执行删除SQL失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID列表批量删除
     * @param tableName 表名
     * @param ids ID列表
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteByIds(String tableName, List<Long> ids) {
        if (!isInitialized()) {
            return 0;
        }

        try {
            int rows = dynamicMssqlMapper.deleteByIds(tableName, ids);
            log.debug("执行批量删除SQL成功，影响{}行", rows);
            return rows;
        } catch (Exception e) {
            log.error("执行批量删除SQL失败，表名: {}, IDs: {}", tableName, ids, e);
            throw new RuntimeException("执行批量删除SQL失败: " + e.getMessage());
        }
    }

    /**
     * 清空指定表的数据
     * @param tableName 表名
     */
    @Transactional(rollbackFor = Exception.class)
    public void truncateTable(String tableName) {
        if (!isInitialized()) {
            return;
        }

        if (tableName == null || tableName.trim().isEmpty()) {
            throw new IllegalArgumentException("表名不能为空");
        }

        String sql = "TRUNCATE TABLE " + tableName;
        try {
            dynamicMssqlMapper.updateBySql(sql, new HashMap<>());
            log.debug("清空表{}数据成功", tableName);
        } catch (Exception e) {
            log.error("清空表{}数据失败", tableName, e);
            throw new RuntimeException("清空表数据失败: " + e.getMessage());
        }
    }

    /**
     * 创建表
     * @param sql 创建表的SQL语句
     * @param params SQL参数
     * @return 是否创建成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createTable(String sql, Map<String, Object> params) {
        if (!isInitialized()) {
            return false;
        }

        validateSql(sql);
        params = validateParams(params);

        // 验证SQL是否为CREATE TABLE语句
        String normalizedSql = sql.trim().toUpperCase();
        if (!normalizedSql.startsWith("CREATE TABLE")) {
            throw new IllegalArgumentException("SQL语句必须是CREATE TABLE语句");
        }

        try {
            dynamicMssqlMapper.updateBySql(sql, params);
            log.debug("创建表成功，SQL: {}", sql);
            return true;
        } catch (Exception e) {
            log.error("创建表失败，SQL: {}, 参数: {}", sql, params, e);
            throw new RuntimeException("创建表失败: " + e.getMessage());
        }
    }

    /**
     * 检查表中是否存在指定主键值的记录
     * @param tableName 表名
     * @param primaryKey 主键列名
     * @param primaryValue 主键值
     * @return 如果存在记录返回true，否则返回false
     */
    public boolean exists(String tableName, String primaryKey, Object primaryValue) {
        if (!isInitialized()) {
            return false;
        }

        if (tableName == null || tableName.trim().isEmpty()) {
            throw new IllegalArgumentException("表名不能为空");
        }
        if (primaryKey == null || primaryKey.trim().isEmpty()) {
            throw new IllegalArgumentException("主键列名不能为空");
        }
        if (primaryValue == null) {
            throw new IllegalArgumentException("主键值不能为空");
        }

        try {
            // 构建查询参数
            Map<String, Object> params = new HashMap<>();
            params.put(primaryKey, primaryValue);

            // 构建查询语句
            String sql = "SELECT 1 FROM " + tableName + " WHERE " + primaryKey + " = #{params." + primaryKey + "}";

            List<Map<String, Object>> result = dynamicMssqlMapper.selectBySql(sql, params);
            boolean exists = result != null && !result.isEmpty();
            log.debug("检查表{}中主键{}={}的记录是否存在，结果: {}", tableName, primaryKey, primaryValue, exists);
            return exists;
        } catch (Exception e) {
            log.error("检查表{}中主键{}={}的记录是否存在失败", tableName, primaryKey, primaryValue, e);
            throw new RuntimeException("检查数据是否存在失败: " + e.getMessage());
        }
    }

    /**
     * 查询单行数据
     * @param sql SQL语句（使用MyBatis参数格式）
     * @param params SQL参数
     * @return 单行数据，如果没有找到返回null
     */
    public Map<String, Object> selectOne(String sql, Map<String, Object> params) {
        if (!isInitialized()) {
            return null;
        }

        validateSql(sql);
        params = validateParams(params);

        try {
            // 检查SQL是否为SELECT语句
            String normalizedSql = sql.trim().toUpperCase();
            if (!normalizedSql.startsWith("SELECT")) {
                throw new IllegalArgumentException("SQL语句必须是SELECT语句");
            }

            // 添加TOP 1（MSSQL语法）
            if (!normalizedSql.contains("TOP ")) {
                sql = sql.replaceFirst("(?i)SELECT", "SELECT TOP 1");
            }

            // 使用selectBySql方法，分页插件不会干扰此方法
            List<Map<String, Object>> result = dynamicMssqlMapper.selectBySql(sql, params);
            if (result == null || result.isEmpty()) {
                log.debug("查询单行数据未找到记录，SQL: {}", sql);
                return null;
            }

            if (result.size() > 1) {
                log.warn("查询单行数据返回多条记录，只返回第一条，SQL: {}", sql);
            }

            log.debug("查询单行数据成功，SQL: {}", sql);
            return result.get(0);
        } catch (Exception e) {
            log.error("查询单行数据失败，SQL: {}, 参数: {}", sql, params, e);
            throw new RuntimeException("查询单行数据失败: " + e.getMessage());
        }
    }

    /**
     * 执行COUNT查询
     * @param sql SQL语句（使用MyBatis参数格式）
     * @param params SQL参数
     * @return 查询结果数量
     */
    public long count(String sql, Map<String, Object> params) {
        if (!isInitialized()) {
            return 0L;
        }

        validateSql(sql);
        params = validateParams(params);

        try {
            // 检查SQL是否为SELECT语句
            String normalizedSql = sql.trim().toUpperCase();
            if (!normalizedSql.startsWith("SELECT")) {
                throw new IllegalArgumentException("SQL语句必须是SELECT语句");
            }

            // 将SQL转换为COUNT查询
            String countSql = "SELECT COUNT(*) as TOTAL FROM (" + sql + ") as temp_table";

            List<Map<String, Object>> result = dynamicMssqlMapper.selectBySql(countSql, params);
            if (result == null || result.isEmpty()) {
                log.debug("执行COUNT查询未找到记录，SQL: {}", countSql);
                return 0L;
            }

            Object count = result.get(0).get("TOTAL");
            if (count == null) {
                log.warn("COUNT查询结果为空，SQL: {}", countSql);
                return 0L;
            }

            long countValue = 0L;
            if (count instanceof Number) {
                countValue = ((Number) count).longValue();
            } else {
                try {
                    countValue = Long.parseLong(count.toString());
                } catch (NumberFormatException e) {
                    log.warn("COUNT查询结果无法转换为数字: {}", count);
                    return 0L;
                }
            }

            log.debug("执行COUNT查询成功，结果: {}", countValue);
            return countValue;
        } catch (Exception e) {
            log.error("执行COUNT查询失败，SQL: {}, 参数: {}", sql, params, e);
            throw new RuntimeException("执行COUNT查询失败: " + e.getMessage());
        }
    }

    /**
     * 检查表是否存在
     * @param tableName 表名
     * @return 是否存在
     */
    public boolean tableExists(String tableName) {
        if (!isInitialized()) {
            return false;
        }

        if (tableName == null || tableName.trim().isEmpty()) {
            throw new IllegalArgumentException("表名不能为空");
        }

        try {
            int count = dynamicMssqlMapper.checkTableExists(tableName);
            boolean exists = count > 0;
            log.debug("检查表{}是否存在，结果: {}", tableName, exists);
            return exists;
        } catch (Exception e) {
            log.error("检查表{}是否存在失败", tableName, e);
            throw new RuntimeException("检查表是否存在失败: " + e.getMessage());
        }
    }

    /**
     * 获取表结构信息
     * @param tableName 表名
     * @return 表结构信息
     */
    public List<Map<String, Object>> getTableStructure(String tableName) {
        if (!isInitialized()) {
            return new java.util.ArrayList<>();
        }

        if (tableName == null || tableName.trim().isEmpty()) {
            throw new IllegalArgumentException("表名不能为空");
        }

        try {
            List<Map<String, Object>> result = dynamicMssqlMapper.getTableStructure(tableName);
            log.debug("获取表{}结构信息成功，字段数: {}", tableName, result.size());
            return result;
        } catch (Exception e) {
            log.error("获取表{}结构信息失败", tableName, e);
            throw new RuntimeException("获取表结构信息失败: " + e.getMessage());
        }
    }

    /**
     * 校验SQL语句
     */
    private void validateSql(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            throw new IllegalArgumentException("SQL语句不能为空");
        }
    }

    /**
     * 校验并处理参数
     */
    private Map<String, Object> validateParams(Map<String, Object> params) {
        return params == null ? new HashMap<>() : params;
    }

    /**
     * 获取数据库配置信息
     * @return 配置信息字符串
     */
    public String getConfigInfo() {
        return MSSQLPropertyHolder.getConfigInfo();
    }

    /**
     * 检查服务是否已初始化
     * @return true if initialized, false otherwise
     */
    public boolean isServiceInitialized() {
        return MSSQLPropertyHolder.isInitialized();
    }

    /**
     * 构建SQL Server 2008兼容的分页SQL
     * 使用ROW_NUMBER()函数实现分页
     */
    private String buildPageSql(String originalSql, int pageNum, int pageSize) {
        // 计算偏移量
        int offset = (pageNum - 1) * pageSize;

        // 检查原始SQL是否已经包含ORDER BY
        String upperSql = originalSql.toUpperCase();
        boolean hasOrderBy = upperSql.contains("ORDER BY");

        String sql = originalSql;
        if (!hasOrderBy) {
            // 如果没有ORDER BY，添加一个默认的排序
            sql = originalSql + " ORDER BY (SELECT NULL)";
        }

        // 构建SQL Server 2008分页SQL
        StringBuilder pageSql = new StringBuilder();
        pageSql.append("SELECT * FROM (");
        pageSql.append("SELECT ROW_NUMBER() OVER (");

        // 提取ORDER BY子句
        int orderByIndex = sql.toUpperCase().lastIndexOf("ORDER BY");
        if (orderByIndex > 0) {
            String orderByClause = sql.substring(orderByIndex);
            String selectPart = sql.substring(0, orderByIndex);

            pageSql.append(orderByClause).append(") AS __row_number__, ");
            // 移除SELECT关键字，保留字段列表
            pageSql.append(selectPart.substring(6).trim());
        } else {
            pageSql.append("ORDER BY (SELECT NULL)) AS __row_number__, ");
            pageSql.append(sql.substring(6).trim());
        }

        pageSql.append(") AS __paged_query__ ");
        pageSql.append("WHERE __row_number__ > ").append(offset);
        pageSql.append(" AND __row_number__ <= ").append(offset + pageSize);

        String result = pageSql.toString();
        log.debug("构建的分页SQL: {}", result);
        return result;
    }
}

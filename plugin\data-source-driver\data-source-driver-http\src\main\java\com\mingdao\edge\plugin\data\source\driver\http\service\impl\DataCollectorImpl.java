package com.mingdao.edge.plugin.data.source.driver.http.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.alipay.sofa.runtime.api.annotation.SofaServiceBinding;
import com.mingdao.edge.core.common.http.RequestUtils;
import com.mingdao.edge.plugin.data.source.driver.api.dto.ApiPageQueryDto;
import com.mingdao.edge.plugin.data.source.driver.api.dto.BasePageQueryDto;
import com.mingdao.edge.plugin.data.source.driver.api.exception.ApiException;
import com.mingdao.edge.plugin.data.source.driver.api.service.DataCollector;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
@Service
@SofaService(bindings = {@SofaServiceBinding(serialize = true)})
public class DataCollectorImpl implements DataCollector {

    @Override
    public JSONObject getPageData(BasePageQueryDto basePageQueryDto) {
        ApiPageQueryDto apiPageQueryDto = (ApiPageQueryDto) basePageQueryDto;

        String url = apiPageQueryDto.getUrl();
        JSONObject params = apiPageQueryDto.getParams();
        JSONObject headers = apiPageQueryDto.getHeader();

        HttpHeaders httpHeaders = new HttpHeaders();
        for (String key : headers.keySet()) {
            httpHeaders.add(key, headers.getString(key));
        }
        if (!headers.containsKey(HttpHeaders.CONTENT_TYPE)) {
            // 设置默认content-type
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        }

        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(params, httpHeaders);

        return RequestUtils.request(url, apiPageQueryDto.getMethod(), httpEntity, JSONObject.class, params);
    }

    @Override
    public JSONObject getOneData(BasePageQueryDto basePageQueryDto) throws ApiException {
        return null;
    }
}

package com.mingdao.edge.core.common.exception;

import com.mingdao.edge.core.common.dto.HttpResponseStatus;
import com.mingdao.edge.core.common.dto.IResponseStatus;
import com.mingdao.edge.core.common.dto.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理自定义业务异常
     */
    @ExceptionHandler(ApplicationException.class)
    @ResponseBody
    public Response<?> handleApplicationException(ApplicationException ex) {
        logger.error("业务异常: {}", ex.getMsg(), ex);
        return Response.error(ex.getMsg());
    }

    /**
     * 处理其他所有异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public Response<?> handleException(Exception ex) {
        logger.error("系统异常: ", ex);
        return Response.error();
    }
} 
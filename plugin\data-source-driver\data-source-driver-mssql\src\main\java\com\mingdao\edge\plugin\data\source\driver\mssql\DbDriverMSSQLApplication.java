package com.mingdao.edge.plugin.data.source.driver.mssql;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * description DbDriverMSSQLApplication
 *
 * <AUTHOR>
 * @since 2025-06-06 17:17:39
 */
@EnableAsync
@MapperScan({"com.mingdao.edge.plugin.data.source.driver.mssql.mapper"})
@SpringBootApplication(scanBasePackages = {"com.mingdao.edge"}, exclude = {DataSourceAutoConfiguration.class})
public class DbDriverMSSQLApplication {
    public static void main(String[] args) {
        SpringApplication.run(DbDriverMSSQLApplication.class, args);
    }
}

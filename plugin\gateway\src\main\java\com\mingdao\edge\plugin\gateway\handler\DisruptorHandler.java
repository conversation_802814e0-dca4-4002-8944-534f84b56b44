package com.mingdao.edge.plugin.gateway.handler;

import com.mingdao.edge.core.api.biz.GatewayDataExchanger;
import com.mingdao.edge.core.api.biz.dto.BaseEvent;
import com.mingdao.edge.core.api.biz.dto.BizMessage;
import com.mingdao.edge.core.common.disruptor.event.DisruptorEvent;
import com.mingdao.edge.core.common.disruptor.handler.DisruptorEventHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 事件处理器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DisruptorHandler implements DisruptorEventHandler<BizMessage<BaseEvent<?>>> {

    private final GatewayDataExchanger gatewayDataExchanger;

    @Override
    public void handle(DisruptorEvent<BizMessage<BaseEvent<?>>> event) {
        BizMessage<BaseEvent<?>> bizMessage = event.getData();
        gatewayDataExchanger.exchange(bizMessage);
        //log.info("处理事件: {}", event);
    }
}

package com.mingdao.edge.plugin.cloud.api;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.alipay.sofa.runtime.api.annotation.SofaServiceBinding;
import com.mingdao.edge.plugin.api.cloud.EwsApi;
import com.mingdao.edge.core.common.dto.BaseResponse;
import com.mingdao.edge.core.common.http.ParamsTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-06-10
 */
@Slf4j
@Service
@SofaService(bindings = {@SofaServiceBinding(serialize = false)})
public class EwsApiImplImpl extends AbstractApiImpl implements EwsApi {

    public BaseResponse<JSONArray> getTaskList() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("limit", "999");
        params.add("orderName", "dt_order");
        //params.add("dt_type", "0");
        //params.add("dt_usefor", "1");
        //params.add("dt_usefor_searchtype", "NI");
        //params.add("dto_source", "1");
        return this.postWithToken(cloudApiProperties.getTasksInitPath() + "/datatask/listsearch.do", params);
    }

    public BaseResponse<JSONObject> getElementList(String module, String page) {

        String url = cloudApiProperties.getInitPath() + module + "/" + page +"/ESpage.do";
        //this.edgeCacheManager.remove(CacheKey.TOKEN);
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        return this.postWithToken(url, params);
    }

    public BaseResponse<JSONArray> getWarehouseProjectList(String dtBasePage, String data_task, Integer transMitStatus, Integer status) {
        dtBasePage = dtBasePage.replaceAll("_", "/") + "/search.do";
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("limit", "999");
        if (!StrUtil.isEmpty(data_task)) {
            params.add("TASKORGNAME", data_task);
        }
        if (transMitStatus != null) {
            params.add("TRANSMITSTATUS", transMitStatus);
        }
        if (transMitStatus != null) {
            params.add("STATUS", status);
        }
        return this.postWithToken(cloudApiProperties.getInitPath() + dtBasePage, params);
    }

    public BaseResponse<JSONArray> getColumns(String dtBasePage) {
        dtBasePage = dtBasePage.replaceAll("_", "/") + "/page.do";
        return this.postWithToken(cloudApiProperties.getInitPath() + dtBasePage, null);
    }

    public BaseResponse<JSONObject> updateSuppliesRecordStatus(Map<String, Object> updateObj) {
        String url = cloudApiProperties.getInitPath()  +"suppliesrecord/transmodform/mod.do";
        updateObj.put("uploadOnly", true);
        return postWithToken(url, ParamsTools.getFormDataMap(updateObj));
    }

    public BaseResponse<JSONArray> searchWarehouseBaseList(JSONObject params) {
        String url = cloudApiProperties.getInitPath()  +"warehouse/baselist/search.do";
        return postWithToken(url, ParamsTools.getFormDataMap(params));
    }

    public BaseResponse<JSONObject> updateWarehouseDownloadStatus(JSONObject updateObj) {
        log.debug("更新入库单档案 AUTOID = [{}]  =================>" , updateObj.get("AUTOID"));
        String url = cloudApiProperties.getInitPath()  +"warehouse/download/mod.do";
        updateObj.put("uploadOnly", true);
        return postWithToken(url, ParamsTools.getFormDataMap(updateObj));
    }


    public BaseResponse<JSONArray> searchSuppliesDetailList(JSONObject params) {
        String url = cloudApiProperties.getInitPath()  +"suppliesrecord/detaillist/search.do";
        params.put("TRANSMITSTATUS", 0);
        return postWithToken(url, ParamsTools.getFormDataMap(params));
    }

    public BaseResponse<JSONObject> updateHistoryStatus(JSONObject params) {
        String url = cloudApiProperties.getInitPath()  +"warehouseProject/updatehistorystatus/mod.do";
        return postWithToken(url, ParamsTools.getFormDataMap(params));
    }
}

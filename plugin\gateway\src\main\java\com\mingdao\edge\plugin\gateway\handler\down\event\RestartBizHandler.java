package com.mingdao.edge.plugin.gateway.handler.down.event;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alipay.sofa.ark.spi.model.BizState;
import com.mingdao.edge.core.api.biz.EdgeGatewayService;
import com.mingdao.edge.core.api.biz.dto.BizInfo;
import com.mingdao.edge.plugin.api.gateway.GatewayDownHandler;
import com.mingdao.edge.plugin.api.gateway.constants.GatewayConstants;
import com.mingdao.edge.plugin.api.gateway.dto.CommandDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@Component(GatewayConstants.GATEWAY_CMD_BEAN_PREFIX + "restart_biz")
public class RestartBizHandler implements GatewayDownHandler {

    @Resource
    private EdgeGatewayService edgeGatewayService;

    @Override
    public Object process(String messageId, CommandDto<?> commandDto) {
        try {
            JSONObject jsonObject = (JSONObject) commandDto.getData();
            String bizName = jsonObject.getString("bizName");
            String bizVersion = jsonObject.getString("version");
            String downloadUrl = jsonObject.getString("downloadUrl");

            BizState bizState = edgeGatewayService.getBizState(bizName, bizVersion);
            if (BizState.ACTIVATED.equals(bizState)) {
                edgeGatewayService.unInstallBiz(bizName, bizVersion);
            }

            BizInfo bizInfo = new BizInfo(bizName, bizVersion, downloadUrl);
            edgeGatewayService.installBiz(bizInfo);

        } catch (Exception e) {
            log.error(StrUtil.format("执行指令异常 {} {}", "RestartBiz", e.getMessage()), e);
        }
        return null;
    }
}

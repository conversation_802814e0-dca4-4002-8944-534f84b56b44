package com.mingdao.edge.core.common.exception;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import java.util.Map;

@SuppressWarnings({"unused", "UnusedReturnValue"})
public class Assert {

    /**
     * 断言是否为真，如果为 {@code false} 抛出 {@code AssertException} 异常<br>
     *
     * <pre class="code">
     * Assert.isTrue(i &gt; 0, "The value must be greater than zero");
     * Assert.isTrue(i &gt; 0, "The value must be greater than {}", 0);
     * </pre>
     *
     * @param expression 布尔值
     * @param msg        异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams  异常信息参数（替换msg中的占位符 {}）
     * @throws AssertException if expression is {@code false}
     */
    public static void isTrue(boolean expression, String msg, Object... msgParams) throws AssertException {
        if (!expression) {
            throw new AssertException(msg, msgParams);
        }
    }

    /**
     * 断言是否为假，如果为 {@code true} 抛出 {@code AssertException} 异常<br>
     *
     * <pre class="code">
     * Assert.isFalse(i &lt; 0, "The value must be greater than zero");
     * Assert.isFalse(i &lt; 0, "The value must be greater than {}", 0);
     * </pre>
     *
     * @param expression 布尔值
     * @param msg        异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams  异常信息参数（替换msg中的占位符 {}）
     * @throws AssertException if expression is {@code false}
     */
    public static void isFalse(boolean expression, String msg, Object... msgParams) throws AssertException {
        if (expression) {
            throw new AssertException(msg, msgParams);
        }
    }

    /**
     * 断言对象是否为{@code null} ，如果不为{@code null} 抛出{@link AssertException} 异常
     *
     * <pre class="code">
     * Assert.isNull(value, "The value must be null");
     * Assert.isNull(value, "The value must be {}", "null");
     * </pre>
     *
     * @param object    被检查的对象
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     * @throws AssertException if the object is not {@code null}
     */
    public static void isNull(Object object, String msg, Object... msgParams) throws AssertException {
        if (null != object) {
            throw new AssertException(msg, msgParams);
        }
    }

    /**
     * 断言对象是否不为{@code null} ，如果为{@code null} 抛出{@link AssertException} 异常 Assert that an object is not {@code null} .
     *
     * <pre class="code">
     * Assert.notNull(clazz, "The class must not be null");
     * Assert.notNull(clazz, "The class must not be {}", "null");
     * </pre>
     *
     * @param <T>       被检查对象泛型类型
     * @param object    被检查对象
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     * @return 被检查后的对象
     * @throws AssertException if the object is {@code null}
     */
    public static <T> T notNull(T object, String msg, Object... msgParams) throws AssertException {
        if (null == object) {
            throw new AssertException(msg, msgParams);
        }
        return object;
    }

    /**
     * 检查给定对象是否为空，为空抛出 {@link AssertException}
     *
     * <pre class="code">
     * Assert.notEmpty(obj, "Object must not be empty");
     * Assert.notEmpty(obj, "Object must not be {}", "empty");
     * </pre>
     *
     * @param <T>       被检查对象泛型类型
     * @param obj       被检查对象
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     * @return 被检查后的对象
     * @throws AssertException 被检查对象为空
     * @see ObjectUtil#isEmpty(Object)
     */
    public static <T> T notEmpty(T obj, String msg, Object... msgParams) throws AssertException {
        if (ObjectUtil.isEmpty(obj)) {
            throw new AssertException(msg, msgParams);
        }
        return obj;
    }

    /**
     * 检查给定字符串是否为空，为空抛出 {@link AssertException}
     *
     * <pre class="code">
     * Assert.notEmpty(name, "Name must not be empty");
     * Assert.notEmpty(name, "Name must not be {}", "empty");
     * </pre>
     *
     * @param <T>       字符串类型
     * @param text      被检查字符串
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     * @return 非空字符串
     * @throws AssertException 被检查字符串为空
     * @see StrUtil#isNotEmpty(CharSequence)
     */
    public static <T extends CharSequence> T notEmpty(T text, String msg, Object... msgParams) throws AssertException {
        if (StrUtil.isEmpty(text)) {
            throw new AssertException(msg, msgParams);
        }
        return text;
    }

    /**
     * 检查给定字符串是否为空白（null、空串或只包含空白符），为空抛出 {@link AssertException}
     *
     * <pre class="code">
     * Assert.notBlank(name, "Name must not be blank");
     * Assert.notBlank(name, "Name must not be {}", "blank");
     * </pre>
     *
     * @param <T>       字符串类型
     * @param text      被检查字符串
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     * @return 非空字符串
     * @throws AssertException 被检查字符串为空白
     * @see StrUtil#isNotBlank(CharSequence)
     */
    public static <T extends CharSequence> T notBlank(T text, String msg, Object... msgParams) throws AssertException {
        if (StrUtil.isBlank(text)) {
            throw new AssertException(msg, msgParams);
        }
        return text;
    }

    /**
     * 断言给定字符串是否不被另一个字符串包含（即是否为子串）
     *
     * <pre class="code">
     * Assert.notContain(name, "rod", "Name must not contain 'rod'");
     * Assert.notContain(name, "rod", "Name must not contain {}", "'rod'");
     * </pre>
     *
     * @param textToSearch 被搜索的字符串
     * @param substring    被检查的子串
     * @param msg          异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams    异常信息参数（替换msg中的占位符 {}）
     * @return 被检查的子串
     * @throws AssertException 非子串抛出异常
     */
    public static String notContain(String textToSearch, String substring, String msg, Object... msgParams) throws AssertException {
        if (StrUtil.contains(textToSearch, substring)) {
            throw new AssertException(msg, msgParams);
        }
        return substring;
    }

    /**
     * 断言给定数组是否包含元素，数组必须不为 {@code null} 且至少包含一个元素
     *
     * <pre class="code">
     * Assert.notEmpty(array, "The array must have elements");
     * Assert.notEmpty(array, "The array must have {}", "elements");
     * </pre>
     *
     * @param <T>       数组元素类型
     * @param array     被检查的数组
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     * @return 被检查的数组
     * @throws AssertException if the object array is {@code null} or has no elements
     */
    public static <T> T[] notEmpty(T[] array, String msg, Object... msgParams) throws AssertException {
        if (ArrayUtil.isEmpty(array)) {
            throw new AssertException(msg, msgParams);
        }
        return array;
    }

    /**
     * 断言给定数组是否不包含{@code null}元素，如果数组为空或 {@code null}将被认为不包含
     *
     * <pre class="code">
     * Assert.noNullElements(array, "The array must have non-null elements");
     * </pre>
     *
     * @param <T>       数组元素类型
     * @param array     被检查的数组
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     * @return 被检查的数组
     * @throws AssertException if the object array contains a {@code null} element
     */
    public static <T> T[] noNullElements(T[] array, String msg, Object... msgParams) throws AssertException {
        if (ArrayUtil.hasNull(array)) {
            throw new AssertException(msg, msgParams);
        }
        return array;
    }

    /**
     * 断言给定集合非空
     *
     * <pre class="code">
     * Assert.notEmpty(collection, "Collection must have elements");
     * </pre>
     *
     * @param <E>        集合元素类型
     * @param <T>        集合类型
     * @param collection 被检查的集合
     * @param msg        异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams  异常信息参数（替换msg中的占位符 {}）
     * @return 非空集合
     * @throws AssertException if the collection is {@code null} or has no elements
     */
    public static <E, T extends Iterable<E>> T notEmpty(T collection, String msg, Object... msgParams) throws AssertException {
        if (CollUtil.isEmpty(collection)) {
            throw new AssertException(msg, msgParams);
        }
        return collection;
    }

    /**
     * 断言给定Map非空
     *
     * <pre class="code">
     * Assert.notEmpty(map, "Map must have entries");
     * </pre>
     *
     * @param <K>       Key类型
     * @param <V>       Value类型
     * @param <T>       Map类型
     * @param map       被检查的Map
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     * @return 被检查的Map
     * @throws AssertException if the map is {@code null} or has no entries
     */
    public static <K, V, T extends Map<K, V>> T notEmpty(T map, String msg, Object... msgParams) throws AssertException {
        if (MapUtil.isEmpty(map)) {
            throw new AssertException(msg, msgParams);
        }
        return map;
    }

    /**
     * 断言给定对象是否是给定类的实例
     *
     * <pre class="code">
     * Assert.instanceOf(Foo.class, foo);
     * </pre>
     *
     * @param <T>  被检查对象泛型类型
     * @param type 被检查对象匹配的类型
     * @param obj  被检查对象
     * @return 被检查的对象
     * @throws AssertException if the object is not an instance of clazz
     * @see Class#isInstance(Object)
     */
    public static <T> T isInstanceOf(Class<?> type, T obj) {
        return isInstanceOf(type, obj, "Object [{}] is not instanceof [{}]", obj, type);
    }

    /**
     * 断言给定对象是否是给定类的实例
     *
     * <pre class="code">
     * Assert.instanceOf(Foo.class, foo);
     * </pre>
     *
     * @param <T>       被检查对象泛型类型
     * @param type      被检查对象匹配的类型
     * @param obj       被检查对象
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     * @return 被检查对象
     * @throws AssertException if the object is not an instance of clazz
     * @see Class#isInstance(Object)
     */
    public static <T> T isInstanceOf(Class<?> type, T obj, String msg, Object... msgParams) throws AssertException {
        notNull(type, "Type to check against must not be null");
        if (!type.isInstance(obj)) {
            throw new AssertException(msg, msgParams);
        }
        return obj;
    }

    /**
     * 断言 {@code superType.isAssignableFrom(subType)} 是否为 {@code true}.
     *
     * <pre class="code">
     * Assert.isAssignable(Number.class, myClass);
     * </pre>
     *
     * @param superType 需要检查的父类或接口
     * @param subType   需要检查的子类
     * @throws AssertException 如果子类非继承父类，抛出此异常
     */
    public static void isAssignable(Class<?> superType, Class<?> subType) throws AssertException {
        isAssignable(superType, subType, "{} is not assignable to {})", subType, superType);
    }

    /**
     * 断言 {@code superType.isAssignableFrom(subType)} 是否为 {@code true}.
     *
     * <pre class="code">
     * Assert.isAssignable(Number.class, myClass);
     * </pre>
     *
     * @param superType 需要检查的父类或接口
     * @param subType   需要检查的子类
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     * @throws AssertException 如果子类非继承父类，抛出此异常
     */
    public static void isAssignable(Class<?> superType, Class<?> subType, String msg, Object... msgParams) throws AssertException {
        notNull(superType, "Type to check against must not be null");
        if (subType == null || !superType.isAssignableFrom(subType)) {
            throw new AssertException(msg, msgParams);
        }
    }

    /**
     * 检查下标（数组、集合、字符串）是否符合要求，下标必须满足：
     *
     * <pre>
     * 0 &le; index &lt; size
     * </pre>
     *
     * @param index     下标
     * @param size      长度
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     * @return 检查后的下标
     * @throws AssertException 如果size &lt; 0 抛出此异常
     * @since 4.1.9
     */
    public static int checkIndex(int index, int size, String msg, Object... msgParams) throws AssertException {
        if (index < 0 || index >= size) {
            throw new AssertException(badIndexMsg(index, size, msg, msgParams));
        }
        return index;
    }

    /**
     * 检查值是否在指定范围内
     *
     * @param value     值
     * @param min       最小值（包含）
     * @param max       最大值（包含）
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     * @return 检查后的长度值
     * @since 4.1.10
     */
    public static int checkBetween(int value, int min, int max, String msg, Object... msgParams) {
        return checkBetween((Integer) value, (Integer) min, (Integer) max, msg, msgParams).intValue();
    }

    /**
     * 检查值是否在指定范围内
     *
     * @param value     值
     * @param min       最小值（包含）
     * @param max       最大值（包含）
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     * @return 检查后的长度值
     * @since 4.1.10
     */
    public static long checkBetween(long value, long min, long max, String msg, Object... msgParams) {
        return checkBetween((Long) value, (Long) min, (Long) max, msg, msgParams).longValue();
    }

    /**
     * 检查值是否在指定范围内
     *
     * @param value     值
     * @param min       最小值（包含）
     * @param max       最大值（包含）
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     * @return 检查后的长度值
     * @since 4.1.10
     */
    public static double checkBetween(double value, double min, double max, String msg, Object... msgParams) {
        return checkBetween((Double) value, (Double) min, (Double) max, msg, msgParams).doubleValue();
    }

    /**
     * 检查值是否在指定范围内
     *
     * @param value     值
     * @param min       最小值（包含）
     * @param max       最大值（包含）
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     * @return 检查后的长度值
     * @since 4.1.10
     */
    public static Number checkBetween(Number value, Number min, Number max, String msg, Object... msgParams) {
        notNull(value, "Value must not be not null");
        notNull(min, "Min must not be not null");
        notNull(max, "Max must not be not null");

        Double valueDouble = value.doubleValue();
        Double minDouble = min.doubleValue();
        Double maxDouble = max.doubleValue();

        if (valueDouble.compareTo(minDouble) < 0 || valueDouble.compareTo(maxDouble) > 0) {
            throw new AssertException(msg, msgParams);
        }
        return value;
    }

    // Private method start --------------------------------------------------------------------------------------------------------------------------------------------

    /**
     * 错误的下标时显示的消息
     *
     * @param index     下标
     * @param size      长度
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     * @return 消息
     */
    private static String badIndexMsg(int index, int size, String msg, Object... msgParams) {
        String formattedMsg = StrUtil.format(msg, msgParams);
        if (index < 0) {
            return formattedMsg + " (index < 0)";
        } else if (size < 0) {
            return formattedMsg + " (size < 0)";
        } else {
            // index >= size
            return formattedMsg + " (index >= size)";
        }
    }
    // Private method end --------------------------------------------------------------------------------------------------------------------------------------------
}

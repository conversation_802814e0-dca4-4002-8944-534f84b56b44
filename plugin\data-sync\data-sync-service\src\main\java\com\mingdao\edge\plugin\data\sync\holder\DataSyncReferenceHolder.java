package com.mingdao.edge.plugin.data.sync.holder;

import com.mingdao.edge.core.common.service.AbstractReferenceServiceHolder;
import com.mingdao.edge.plugin.api.data.sync.service.DataSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 下行数据的业务处理器
 * 这些业务处理器都是biz包，需通过SofaArk ReferenceClient来调用
 *
 * <AUTHOR>
 * @since 2022/7/19 8:54
 */
@Slf4j
@Service
@SuppressWarnings({"rawtypes", "unchecked"})
public class DataSyncReferenceHolder extends AbstractReferenceServiceHolder<DataSyncService> {
}

package com.mingdao.edge.plugin.gateway.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.ArrayUtil;
import com.mingdao.edge.core.common.constants.EdgeConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.filefilter.FileFilterUtils;

import java.io.File;
import java.io.FileFilter;
import java.io.IOException;

@Slf4j
public class BizFileUtil {

    /**
     * jar包后缀
     */
    private static final String ARK_SUFFIX = "-ark-biz.jar";

    private static final File PLUGIN_DIR_FILE = FileUtil.file(EdgeConstant.PLUGIN_PATH);


    public static boolean existsBiz(String filePath) {
        return FileUtil.file(filePath).exists();
    }


    /**
     * 删除下载的biz包
     * @param bizName
     * @param bizVersion
     */
    public static void deleteDownLoadBiz(String bizName, String bizVersion) {
        log.info("开始删除下载的biz包：bizName = {}, bizVersion = {}", bizName, bizVersion);
        // 删除下载的biz包
        try {
            if (!FileUtil.del(getDownLoadPath(bizName, bizVersion))) {
                log.info("下载的biz包删除失败：bizName = {}, bizVersion = {}", bizName, bizVersion);
                return;
            }
            log.info("下载的biz包删除成功：bizName = {}, bizVersion = {}", bizName, bizVersion);
        } catch (IORuntimeException e) {
            log.error("下载的biz包删除失败：", e);
        }
    }

    /**
     * 删除已经卸载的biz程序目录
     * @param bizName
     * @param bizVersion
     */
    public static void deleteUninstallBizDir(String bizName, String bizVersion) {
        // 过滤出以指定字符串开头的文件夹，将文件夹删除

        FileFilter fileFilter = FileFilterUtils.prefixFileFilter(getBizFilePrefix(bizName, bizVersion));
        File[] needDeleteFiles = PLUGIN_DIR_FILE.listFiles(fileFilter);

        if (ArrayUtil.isEmpty(needDeleteFiles)) {
            return;
        }

        for (File needDeleteFile : needDeleteFiles) {
            if (!needDeleteFile.isDirectory()) {
                continue;
            }
            try {
                if (needDeleteFile.isDirectory()) {
                    log.info("开始删除旧的biz文件夹：{}", needDeleteFile.getName());
                    FileUtils.forceDelete(needDeleteFile);
                    log.info("旧biz文件夹： {} 删除成功：", needDeleteFile.getName());
                }
            } catch (IOException ignored) {
                // 文件删除失败直接忽略
                // ignored.printStackTrace();
            }
        }
    }

    /**
     * 删除biz程序目录
     */
    public static void deleteAllUninstallBizDir() {
        // 过滤出以指定字符串开头的文件夹，将文件夹删除

        File[] needDeleteFiles = PLUGIN_DIR_FILE.listFiles();

        if (ArrayUtil.isEmpty(needDeleteFiles)) {
            return;
        }

        for (File needDeleteFile : needDeleteFiles) {
            if (!needDeleteFile.isDirectory()) {
                continue;
            }
            try {
                if (needDeleteFile.isDirectory()) {
                    log.info("开始删除旧的biz文件夹：{}", needDeleteFile.getName());
                    FileUtils.forceDelete(needDeleteFile);
                    log.info("旧biz文件夹： {} 删除成功：", needDeleteFile.getName());
                }
            } catch (IOException ignored) {
                // 文件删除失败直接忽略
                // ignored.printStackTrace();
            }
        }
    }

    /**
     * 获取biz包下载的绝对路径
     * @param bizName
     * @param bizVersion
     * @return
     */
    public static String getDownLoadPath(String bizName, String bizVersion) {
        return EdgeConstant.DOWNLOAD_PATH + getBizFileName(bizName, bizVersion);
    }

    /**
     * 拼接文件名和后缀
     *
     * @param fileName
     * @return
     */
    public static String getBizFileName(String fileName, String version) {
        return getBizFilePrefix(fileName, version) + ARK_SUFFIX;
    }

    private static String getBizFilePrefix(String fileName, String version) {
        return fileName + "-" + version;
    }
}

package com.mingdao.edge.plugin.api.gateway.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * description QueryModel
 *
 * <AUTHOR>
 * @since 2024/10/27 12:46
 */
@Data
public class QueryModel implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonProperty(value = "page", access = JsonProperty.Access.WRITE_ONLY)
    private Integer page = 0;

    @JsonProperty(value = "limit", access = JsonProperty.Access.WRITE_ONLY)
    private Integer limit;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String orderName;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String orderType;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Boolean orderAsc;
}

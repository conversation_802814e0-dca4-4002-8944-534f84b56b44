<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.mingdao.edge</groupId>
        <artifactId>core</artifactId>
        <version>${revision}</version>
    </parent>

    <groupId>com.mingdao.edge.core</groupId>
    <artifactId>common-cache</artifactId>
    <version>${common-cache.version}</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.ehcache</groupId>
            <artifactId>ehcache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <!--<dependency>-->
        <!--    <groupId>javax.cache</groupId>-->
        <!--    <artifactId>cache-api</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
    </dependencies>

</project>

package com.mingdao.edge.core.api.biz.constant;


/**
 * <AUTHOR>
 * @desc desc
 */
public interface BizCacheConstant {

    /**
     * 全局使用缓存key
     */

    /**
     * 设备信息已经通过h2进行缓存，不再进行内存缓存
     * 全局信息缓存key
     */
    @Deprecated
    String COMMON_CACHE_KEY = "commonCacheKey";

    /**
     * 设备信息已经通过h2进行缓存，不再进行内存缓存
     * 设备列表缓存key
     */
    @Deprecated
    String EDGE_DEVICE_LIST = "edgeDeviceList";

    /**
     * 脚本解析模块使用的应用名
     */
    String SCRIPT_APPLICATION_NAME = "yhlz-iot-edge-script-transorm";

    /**
     * 设备 设备唯一标识符（通过脚本解析之后，反射拿到deviceCode的值） -> deviceKey
     */
    String DEVICE_UNIQUE_IDENTIFIED_DEVICE_KEY = "deviceSerialNumDeviceKey";

    /**
     * 设备 deviceKey -> 设备签名
     */
    String DEVICE_KEY_SIGNATURE = "deviceKeySignature";

    /**
     * 设备key -> 设备实体
     */
    String DEVICE_KEY_ENTITY = "deviceKeyEntity";

    /**
     * 设备key -> bizName
     */
    String DEVICE_KEY_BIZ_NAME = "deviceKeyBizName";

    /**
     * 设备key -> productKey
     */
    String DEVICE_KEY_PRODUCT_KEY = "deviceKeyProductKey";

    /**
     * 产品key -> 产品标识符
     */
    String PRODUCT_KEY_IDENTIFIER = "productKeyIdentifier";

    // edge-mina-client 缓存的key
    interface EdgeClientCacheKey {

        /**
         * 许可证信息
         */
        String LICENSE = "license";

        /**
         * edge边缘服务相关配置信息key
         */
        String EDGE_PUBLIC_KEY = "edgePublicKey";

        /**
         * 网关key
         */
        String EDGE_KEY = "edgeKey";

        /**
         * appId
         */
        String EDGE_APP_ID = "edgeAppId";

        /**
         * appSecurity
         */
        String EDGE_APP_SECURITY = "edgeAppSecurity";

    }

    // 设备模组 缓存的key
    interface DeviceModuleCacheKey {

        String DEVICE_MODULE_SUFFIX = "_deviceModule";

        /**
         * 配置id与配置参数的缓存key
         */
        String CONFIG_ID_CONFIG_PARAM = "configIdConfigParam" + DEVICE_MODULE_SUFFIX;

        /**
         * connectorId对应的产品key，缓存后缀
         */
        String PRODUCT_KEY_SUFFIX = "_productKey" + DEVICE_MODULE_SUFFIX;

        /**
         * connectorId 对应设备key，缓存后缀
         */
        String DEVICE_KEY_SUFFIX = "_deviceKey" + DEVICE_MODULE_SUFFIX;

        /**
         * deviceKey 对应的 configId
         */
        String DEVICE_KEY_CONNECTOR_ID = "deviceKeyConnectorId";

        /**
         * 模组版本缓存key
         */
        String MODULE_VERSION = "moduleVersion";

        /**
         * 模组配置
         */
        String MODULE_CONFIG = "moduleConfig";

        /**
         * 模组设备列表
         */
        String MODULE_DEVICE_LIST = "moduleDeviceList";
    }


}

package com.mingdao.edge.plugin.data.source.driver.api.service;

import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.plugin.data.source.driver.api.dto.BasePageQueryDto;
import com.mingdao.edge.plugin.data.source.driver.api.exception.ApiException;

/**
 * 数据采集器
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
public interface DataCollector {
    JSONObject getPageData(BasePageQueryDto basePageQueryDto) throws ApiException;
    JSONObject getOneData(BasePageQueryDto basePageQueryDto) throws ApiException;
}

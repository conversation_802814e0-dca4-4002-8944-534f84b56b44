package com.mingdao.edge.core.common.dto;

import com.mingdao.edge.core.common.constants.RequestResult;
import lombok.Data;

import java.io.Serializable;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
public class BaseResponse<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer res;
    private Integer code;
    private Long count;
    private String resMsg;
    private String msg;
    private T data;

    public static <T> BaseResponse<T> ok(T data) {
        return restResult(data, RequestResult.RES_SUCCESS, null);
    }
    private static <T> BaseResponse<T> restResult(T data, int code, String msg) {
        BaseResponse<T> apiResult = new BaseResponse<>();
        apiResult.setCode(code);
        apiResult.setRes(code);
        apiResult.setData(data);
        apiResult.setMsg(msg);
        return apiResult;
    }
}

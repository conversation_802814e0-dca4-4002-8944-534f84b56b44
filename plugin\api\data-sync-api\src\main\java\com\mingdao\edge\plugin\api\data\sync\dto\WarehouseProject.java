package com.mingdao.edge.plugin.api.data.sync.dto;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 入库/出库方案配置
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Data
public class WarehouseProject implements Serializable {
    private static final long serialVersionUID = 1L;

    private String TASKORGTITLE;
    private String STATUS_datasource;
    private Long AUTOID;
    private Date SYS_MODIFY_DATE;
    private String SYS_MODIFIER;
    private Integer HISTORYSTATUS;
    private String NAME;
    private String QUERYEXPRESSION;
    private String CODE;
    private Integer STATUS;
    private String TASKORGNAME;
    private Date SYS_CREATE_DATE;
    private Integer TASKORGID;
    private Integer TYPE;
    private String SYS_CREATOR;
    private String TRANSFERFIELD;
    private List<TransferField> transferFieldList;


    private String queryString;
    public String getQueryString() {
        if (this.queryString != null) {
            return this.queryString;
        }
        String queryExpression = this.getQUERYEXPRESSION();
        if (!StrUtil.isBlank(queryExpression)) {
            if (StrUtil.isBlank(queryExpression)) return queryExpression;
            if (queryExpression.equals("{}")) return queryExpression;

            Map<String, String> keyValueMap = new HashMap<>();
            queryExpression = queryExpression.replaceAll("[{}\"]", "");
            String[] pairs = queryExpression.split(",");

            //20250113修复,多参数问题
            String nowParam = "";
            for (String pair : pairs) {
                if (pair.contains(":")) {
                    String[] keyValue = pair.split(":");
                    String key = keyValue[0];
                    String value = "";
                    if (keyValue.length == 2) {
                        value = keyValue[1];
                    }
                    keyValueMap.put(key, value.trim());
                    nowParam = keyValue[0].trim();
                } else {
                    if (StrUtil.isNotBlank(nowParam)) {
                        keyValueMap.put(nowParam, keyValueMap.get(nowParam) + "," + pair);
                    }
                }
            }

            StringBuilder queryStringBuilder = new StringBuilder();
            for (Map.Entry<String, String> entry : keyValueMap.entrySet()) {
                queryStringBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
            this.queryString = queryStringBuilder.toString().trim();

        }
        return this.queryString;
    }

    @JSONField(serialize = false)
    public List<TransferField> getTransferFieldList() {
        if (this.transferFieldList == null) {
            transferFieldList = JSON.parseArray(TRANSFERFIELD, TransferField.class);
        }
        return transferFieldList;
    }

    public JSONObject convertToUserData(JSONObject dataItem, Map<String, ColTemp> fieldMap) {
        JSONObject userData = new JSONObject();
        this.getTransferFieldList().forEach(field -> {
            // 这是云端字段名称
            String targetFieldName = field.getTargetField();
            // 这是客户侧字段名称
            String sourceFieldName = field.getSourceField();
            ColTemp colTemp = fieldMap.get(sourceFieldName);

            Object sourceValue = dataItem.get(sourceFieldName);
            if (ObjectUtil.isEmpty(sourceValue)) {
                // 没配置云端关联字段，先找方案中的默认值
                String defaultValue = field.getDefaultValue();
                if (StrUtil.isNotBlank(defaultValue)) {
                    sourceValue = defaultValue;
                }
            }

            // 数字类型时，如果值为空字符串，则设为null
            if (Boolean.TRUE.equals(colTemp.getIsNumber())
                    && sourceValue instanceof String
                    && StrUtil.isBlank((String) sourceValue)) {
                sourceValue = null;
            }
            userData.put(sourceFieldName, sourceValue);
        });
        return userData;
    }
}

package com.mingdao.edge.plugin.data.sync.handler.cmd;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.mingdao.edge.core.common.exception.Assert;
import com.mingdao.edge.plugin.api.data.sync.dto.DataBatchCmdDto;
import com.mingdao.edge.plugin.api.data.sync.service.DataSyncService;
import com.mingdao.edge.plugin.api.gateway.GatewayDownHandler;
import com.mingdao.edge.plugin.api.gateway.constants.GatewayConstants;
import com.mingdao.edge.plugin.api.gateway.dto.CommandDto;
import com.mingdao.edge.plugin.data.sync.context.TaskContext;
import com.mingdao.edge.plugin.data.sync.context.TaskContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@Service
@SofaService(uniqueId = GatewayConstants.GATEWAY_CMD_BEAN_PREFIX + "data_reupload")
public class DataReUploadHandler implements GatewayDownHandler {

    @Resource
    private TaskContextHolder taskContextHolder;

    @Override
    public Object process(String messageId, CommandDto<?> commandDto) {
        try {
            Assert.notNull(commandDto, "参数不能为空");

            Object data = commandDto.getData();
            DataBatchCmdDto dataBatchCmdDto = null;
            if (data instanceof JSONObject) {
                JSONObject jsonObject = (JSONObject) commandDto.getData();
                dataBatchCmdDto = jsonObject.toJavaObject(DataBatchCmdDto.class);
            }

            Assert.notNull(dataBatchCmdDto, "参数不能为空");
            Assert.notNull(dataBatchCmdDto.getDtName(), "参数不能为空");
            Assert.notNull(dataBatchCmdDto.getSysOrg(), "参数不能为空");
            Assert.notNull(dataBatchCmdDto.getIds(), "参数不能为空");

            TaskContext taskContext = taskContextHolder.getTaskContext(dataBatchCmdDto.getDtName());
            DataSyncService dataSyncService = taskContext.getDataSyncService();
            dataBatchCmdDto.getIds().forEach(id -> dataSyncService.sendToDataConvertor(id, dataSyncService.getUpTopic()));

        } catch (Exception e) {
            log.error(StrUtil.format("执行指令异常 {} {}", "data_priority_upload", e.getMessage()), e);
        }
        return null;
    }
}

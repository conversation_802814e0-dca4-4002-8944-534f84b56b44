package com.mingdao.edge.plugin.data.sync.base.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.sofa.runtime.api.annotation.SofaReference;
import com.mingdao.edge.core.api.biz.BizCache;
import com.mingdao.edge.core.api.biz.dto.BizMessage;
import com.mingdao.edge.core.api.biz.enums.BizUnique;
import com.mingdao.edge.core.common.dto.BaseResponse;
import com.mingdao.edge.core.common.exception.ApplicationException;
import com.mingdao.edge.core.common.exception.Assert;
import com.mingdao.edge.core.pulsar.utils.PulsarClientUtil;
import com.mingdao.edge.plugin.api.cloud.EwsApi;
import com.mingdao.edge.plugin.api.data.sync.constants.DataState;
import com.mingdao.edge.plugin.api.data.sync.constants.MDColumn;
import com.mingdao.edge.plugin.api.data.sync.dto.ColTemp;
import com.mingdao.edge.plugin.api.data.sync.service.DataSyncService;
import com.mingdao.edge.plugin.api.gateway.DisruptorApi;
import com.mingdao.edge.plugin.api.gateway.EdgeStateService;
import com.mingdao.edge.plugin.api.gateway.constants.PulsarNamespace;
import com.mingdao.edge.plugin.api.gateway.dto.DataCollectEvent;
import com.mingdao.edge.plugin.api.gateway.enums.StateEnum;
import com.mingdao.edge.plugin.api.mapper.constants.SqlTemplateConstant;
import com.mingdao.edge.plugin.api.mapper.service.DataBaseService;
import com.mingdao.edge.plugin.api.mapper.service.DataLogService;
import com.mingdao.edge.plugin.data.sync.base.constants.CacheKey;
import com.mingdao.edge.plugin.data.sync.base.constants.DataSyncConstants;
import com.mingdao.edge.plugin.data.sync.base.constants.EventTypeConstants;
import com.mingdao.edge.plugin.data.sync.base.dto.SysField;
import com.mingdao.edge.plugin.data.sync.base.utils.DownloadCloudTools;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Slf4j
public abstract class AbstractDataSync implements DataSyncService {

    @SofaReference
    protected DataBaseService dataBaseService;
    @SofaReference
    protected DataLogService dataLogService;
    @SofaReference
    protected DisruptorApi disruptorApi;
    @SofaReference
    protected BizCache bizCache;
    @SofaReference
    protected EwsApi ewsApi;
    @SofaReference
    private EdgeStateService edgeStateService;

    protected JSONObject taskInfo;

    /**
     * dtName的前半部分
     */
    protected String moduleName;
    /**
     * dtName的后半部分
     */
    protected String pageName;
    protected String className;
    protected String tableName;
    protected String insertSql;
    protected String sysOrg;
    protected String dtName;

    protected final Integer pageSize;

    protected JSONObject viewData;
    protected String url;
    protected String dtKey;

    @Getter
    protected String dataType;
    @Getter
    private String upTopic;

    private final String uniqueId;
    //protected List<MoTemp> moTempColumns = null;
    protected final Map<String, ColTemp> fieldMap = new HashMap<>();

    public AbstractDataSync(String moduleName, String pageName, Integer pageSize) {

        this.moduleName = moduleName;
        this.pageName = pageName;
        this.className = moduleName + "_" + pageName;
        this.pageSize = pageSize;

        this.uniqueId = StrUtil.format("{}_{}", DataSyncConstants.DATA_SYNC_SERVICE, className);
    }

    public void init(JSONObject taskInfo) {
        log.debug("[初始化同步实现类][{}]", className);

        this.taskInfo = taskInfo;
        this.dataType = taskInfo.getString("dt_tobasepage");
        sysOrg = taskInfo.getString("dto_org");
        Assert.notNull(sysOrg, "dto_org不能为空");

        // 只有上行才有
        this.upTopic = dataType != null ? PulsarClientUtil.getFullTopic(PulsarClientUtil.getTenantName(sysOrg), PulsarNamespace.DATA_UP, "data") : null;

        // 获取字段及其他配置
        BaseResponse<JSONObject> response = ewsApi.getElementList(moduleName, pageName);
        if (response == null || ObjectUtil.isEmpty(response)
                || response.getData() == null || ObjectUtil.isEmpty(response.getData())) {
            throw new ApplicationException(StrUtil.format("[数据同步][{}]模版未正常初始化", className));
        }
        viewData = response.getData();

        // 缓存字段信息
        bizCache.setValue(className, "version", CacheKey.SYNC_TEMPLATE + className, viewData, 60);

        dtName = taskInfo.getString("dt_name");
        dtKey = taskInfo.getString("dt_key");
        url = viewData.getString("view");
        //url = viewData.getString("view");
        //JSONArray list = viewData.getJSONArray("list");
        //String key = viewData.getString("key");

        // 先建表
        createDataTableIfNotExist(viewData, fieldMap);
        log.debug("[初始化同步实现类][{}] 完成初始化 {}", className, response);
    }

    protected void createDataTableIfNotExist(JSONObject viewData, Map<String, ColTemp> fieldMap) {
        JSONArray fieldArray = viewData.getJSONArray("list");

        if (ArrayUtil.isEmpty(fieldArray)) {
            throw new RuntimeException(StrUtil.format("[初始化同步实现类][{}]字段数据为空", className));
        }

        tableName = "tb_" + dtName;

        //moTempColumns = new ArrayList<>();

        StringBuilder sql = new StringBuilder("\n id bigint auto_increment primary key,");

        String insertSqlParse = "INSERT INTO {} ({}) VALUES ({})";
        StringBuilder fieldSql = new StringBuilder();
        StringBuilder valuesSql = new StringBuilder();

        for (int i = 0; i < fieldArray.size(); i++) {
            JSONObject field = fieldArray.getJSONObject(i);

            ColTemp moColumnTemp = field.toJavaObject(ColTemp.class);
            String columnName = moColumnTemp.getColumnName();

            if (1 == moColumnTemp.getDe_group()
                    && StrUtil.isNotEmpty(columnName)
                    && !"password".equals(moColumnTemp.getDe_type())) {
                //moTempColumns.add(moColumnTemp);

                fieldMap.put(columnName, moColumnTemp);

                String columnType = "VARCHAR(512)";
                if ("Double".equalsIgnoreCase(moColumnTemp.getDe_df_code())) {
                    columnType = "DECIMAL(20,4)";
                } else if ("Integer".equalsIgnoreCase(moColumnTemp.getDe_df_code())) {
                    columnType = "BIGINT";
                } else if ("Text".equalsIgnoreCase(moColumnTemp.getDe_df_code())) {
                    columnType = "TEXT";
                } else {
                    columnType = "VARCHAR(512)";
                }
                sql.append("\n").append(columnName).append(" ").append(columnType);

                // 处理约束（如NOT NULL）
                Integer deNecessary = moColumnTemp.getDe_necessary();
                if (deNecessary == 1) {
                    sql.append(" NOT NULL");
                }

                //if (StrUtil.isNotBlank(dtKey) && columnName.equalsIgnoreCase(dtKey)) {
                //    sql.append(" PRIMARY KEY");
                //}

                // 添加注释（可选）
                String deTitle = moColumnTemp.getDe_title();
                if (!StrUtil.isBlank(deTitle)) {
                    sql.append(" COMMENT '").append(deTitle).append("'");
                }
                sql.append(",\n");

                fieldSql.append(columnName).append(", ");
                valuesSql.append("#{params.").append(columnName).append("}, ");
            }
        }

        List<SysField> sysFields = getSysFields();
        for (int j = 0; j < sysFields.size(); j++) {
            SysField sysField = sysFields.get(j);
            fieldSql.append(sysField.getFieldName());
            valuesSql.append("#{params.").append(sysField.getFieldName());
            sql.append(sysField.toDDL());
            if (j < sysFields.size() - 1) {
                fieldSql.append(",");
                valuesSql.append("}, ");
                sql.append(",");
            } else {
                fieldSql.append(" ");
                valuesSql.append("} ");
            }
        }

        if (sql.length() > 0) {
            createTable(tableName, sql.toString());
        }
        this.insertSql = StrUtil.format(insertSqlParse, tableName,
                fieldSql.toString(),
                valuesSql.toString());
    }

    protected List<SysField> getSysFields() {
        List<SysField> list = new ArrayList<>();
        list.add(new SysField(MDColumn.SYS_ORG, "VARCHAR(32)", true));
        list.add(new SysField(MDColumn.MD5, "VARCHAR(32)", true));
        list.add(new SysField(MDColumn.CREATE_TIME, "DATETIME", true));
        list.add(new SysField(MDColumn.DT_NAME, "VARCHAR(32)", true));
        list.add(new SysField(MDColumn.STATE, "VARCHAR(32)", true));
        return list;
    }

    protected void createTable(String tableName, String fieldSql) {
        String createTableSql = StrUtil.format(SqlTemplateConstant.CREATE_TABLE, tableName, fieldSql);
        dataBaseService.createTable(createTableSql, null);
    }

    public void clearData() {
        dataBaseService.truncateTable(this.tableName);
    }

    protected void processData(JSONObject data) {
        try {
            // 保存数据
            Long id = convertDataAndSave(data, fieldMap);
            if (id != null) {
                //dataLogService.addDataLogMsg((Long) id, DataSyncConstants.DATA_SYNC_SERVICE, className, data.getMsg());
                // 通知转换数据
                sendToDataConvertor(id, this.upTopic);
            }
        } catch (Exception e) {
            log.error(StrUtil.format("[获取客户数据][{}]异常: {}", className, e.getMessage()), e);
        }
    }


    /**
     * 转换数据并保存到数据库
     * 例如上行：将用户方数据转为云端结构并保存
     * 为什么不直接保存客户数据？因为表结构在获取数据前已经定义好
     * @param data 原始数据
     * @param fieldMap 任务配置的字段
     * @return
     */
    protected abstract Long convertDataAndSave(JSONObject data, Map<String, ColTemp> fieldMap);
    /**
     * 转换特殊字段，例如是/否转换成1/0
     * @param fieldName
     * @param value
     * @param result
     * @return
     */
    protected abstract boolean convertSpecField(String fieldName, Object value, Map<String, Object> result);

    protected void addSysFields(String dtName, Map<String, Object> result, JSONObject item) {
        String dataState = DataState.WAITING_CONVERT;
        String md5 = DigestUtil.md5Hex(new JSONObject(result).toJSONString());
        result.put(MDColumn.MD5, md5);
        result.put(MDColumn.SYS_ORG, this.sysOrg);
        result.put(MDColumn.STATE, dataState);
        result.put(MDColumn.CREATE_TIME, DateUtil.format(new Date(), DatePattern.NORM_DATETIME_MS_FORMAT));
        result.put(MDColumn.DT_NAME, dtName);
    }

    protected Long saveData(Map<String, Object> data) {
        String md5 = data.get(MDColumn.MD5).toString();
        boolean exists = dataBaseService.exists(tableName, MDColumn.MD5, md5);
        if (!exists) {
            Long id = dataBaseService.insert(this.insertSql, data);
            try {
                addLog(sysOrg, id, DataState.WAITING_CONVERT);
                //addLogMsg((Long) id, "客户端保存成功");
            } catch (Exception e) {
                log.error(StrUtil.format("[保存上行数据][保存日志]异常<{}>: {}", className, e.getMessage()), e);
            }
            return id;
        }
        log.warn("重复数据: {} {}", className, md5);
        return null;
    }

    protected void updateState(Object id, String dataState) {
        if (Objects.isNull(id)) {
            log.warn("[更新数据状态][{}]id为空", className);
            return;
        }
        String updateSql = "update " + tableName + " set " + MDColumn.STATE + " = #{params.state} where id = #{params.id}";
        Map<String, Object> params = new HashMap<>();
        params.put("state", dataState);
        params.put("id", id);
        dataBaseService.update(updateSql, params);
    }

    protected void addLog(String orgCode, Long id, String dataState) {
        dataLogService.addDataLog(orgCode, DataSyncConstants.DATA_SYNC_SERVICE, className, id, dataState, dtName);
    }
    protected void addLogMsg(Long id, String msg) {
        dataLogService.addDataLogMsg(id, DataSyncConstants.DATA_SYNC_SERVICE, className, msg);
    }

    public Long getLastDataTime() {
        String cacheKey = CacheKey.LAST_SYNC_TIME + className;
        Long lastTime = bizCache.getValue(className, "version", cacheKey, Long.class);
        if (Objects.isNull(lastTime)) {
            lastTime = DateUtil.parseDateTime("2025-03-01 00:00:00").getTime();
            String sql = "select MD_CREATE_TIME from " + tableName + " order by MD_CREATE_TIME desc limit 1";
            Map<String, Object> result = dataBaseService.selectOne(sql, null);
            if (result != null && !result.isEmpty()) {
                String dateStr = (String) result.get(MDColumn.CREATE_TIME);
                if (!StrUtil.isEmpty(dateStr)) {
                    Date time = DateUtil.parseDateTime(dateStr);
                    lastTime = time.getTime();
                    bizCache.setValue(className, "version", cacheKey, lastTime);
                    log.debug("获取到最后数据时间: {}", lastTime);
                }
            }
        }
        return lastTime;
    }

    public void updateLastTime(long time) {
        String cacheKey = CacheKey.LAST_SYNC_TIME + className;
        Long lastTime = bizCache.getValue(className, "version", cacheKey, Long.class);
        if (lastTime == null || time > lastTime) {
            bizCache.setValue(className, "version", cacheKey, time);
            Date date = new Date(time);
            edgeStateService.putState(StateEnum.DATA_LAST_QUERY_TIME, className, DateUtil.format(date, DatePattern.NORM_DATETIME_FORMAT), date, true, Long.valueOf(sysOrg));
        }
    }

    /**
     * 检查保存后的数据，如果已有数据则设为重复数据
     *
     * @param id
     * @return
     */
    protected Map<String, Object> getSavedData(Long id) {
        try {
            String selectSql = "select * from " + tableName + " where id = #{params.id}";
            Map<String, Object> map = new HashMap<>(1);
            map.put("id", id);
            Map<String, Object> item = dataBaseService.selectOne(selectSql, map);

            // 处理text字段为Clob的情况，转为String
            if (item != null) {
                DownloadCloudTools.convertClobToString(item);
                String md5 = (String) item.get(MDColumn.MD5);
                long existsCount = existCountByMd5(id, md5);
                if (existsCount <= 1) {
                    return item;
                }
                // 更新该行数据为重复数据
                updateState(id, DataState.DUPLICATE);
            }


        } catch (Exception e) {
            log.error(StrUtil.format("[保存上行数据][检查重复数据]异常<{}>: {}", className, e.getMessage()), e);
        }
        return null;
    }

    public void sendToDataConvertor(Long id, String upTopic) {
        BizMessage<Object> messageDTO = new BizMessage<>();
        messageDTO.setSourceBiz(BizUnique.DATA_COLLECTOR);
        messageDTO.setTargetBiz(BizUnique.DATA_COLLECTOR);

        DataCollectEvent dataCollectEvent = DataCollectEvent.buildUpEvent(id, className, dataType, upTopic, EventTypeConstants.DISRUPTOR_DATA_PROCESS);

        messageDTO.setData(dataCollectEvent);
        disruptorApi.publish(messageDTO);

        //EventAdminService eventAdminService = ArkClient.getEventAdminService();
        //eventAdminService.sendEvent(new DataEvent(dataCollectorEventDTO));
    }

    /**
     * 获取重试数据
     *
     * @return
     */
    protected List<Map<String, Object>> getRetryDataList() {
        // 计算1天前的时间
        Date startTime = DateUtil.offsetDay(new Date(), -1);
        Date endTime = DateUtil.offsetHour(new Date(), -1);
        String updateSql = "select id from " + tableName;

        updateSql += " where ((" + MDColumn.STATE + " = #{params.stateFail} and " + MDColumn.CREATE_TIME + " > #{params.createTime}) or (" + MDColumn.STATE + " = #{params.stateUploadSuccess} and " + MDColumn.CREATE_TIME + " between #{params.createTime} and #{params.endTime}))";

        Map<String, Object> params = new HashMap<>();
        params.put("stateFail", DataState.SYNC_FAIL);
        params.put("stateUploadSuccess", DataState.UPLOAD_SUCCESS);
        params.put("createTime", DateUtil.format(startTime, "yyyy-MM-dd HH:mm:ss"));
        params.put("endTime", DateUtil.format(endTime, "yyyy-MM-dd HH:mm:ss"));
        return dataBaseService.select(updateSql, params);
    }

    @Override
    public void transFailData() {
        List<Map<String, Object>> failData = getRetryDataList();
        if (failData != null && !failData.isEmpty()) {
            for (Map<String, Object> map : failData) {
                Long id = (Long) map.get("id");
                if (!ObjectUtil.isNull(id)) {
                    sendToDataConvertor(id, this.upTopic);
                }
            }
        }
    }

    protected Long existCountByMd5(Object id, String md5) {
        // 查询是否有其他待传输的数据
        String dSql = "select * from " + tableName + " where " + MDColumn.MD5 + " = #{params.md5} and id != #{params.id} and " + MDColumn.STATE + " != #{params.state}";

        Map<String, Object> md5Params = new HashMap<>(3);
        md5Params.put("id", id);
        md5Params.put("md5", md5);
        md5Params.put("state", DataState.DUPLICATE);

        return dataBaseService.count(dSql, md5Params);
    }
}

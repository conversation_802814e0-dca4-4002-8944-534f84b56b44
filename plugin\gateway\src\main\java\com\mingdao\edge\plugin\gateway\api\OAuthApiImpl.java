package com.mingdao.edge.plugin.gateway.api;

import cn.hutool.core.util.StrUtil;
import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.alipay.sofa.runtime.api.annotation.SofaServiceBinding;
import com.mingdao.edge.core.common.cache.EdgeCacheManager;
import com.mingdao.edge.core.common.context.SpringContextUtils;
import com.mingdao.edge.core.common.exception.ErrorCode;
import com.mingdao.edge.core.common.exception.InvalidTokenException;
import com.mingdao.edge.core.common.http.RequestUtils;
import com.mingdao.edge.plugin.api.gateway.OAuthApi;
import com.mingdao.edge.plugin.api.gateway.dto.OAuthDto;
import com.mingdao.edge.plugin.gateway.constants.CacheKey;
import com.mingdao.edge.plugin.gateway.property.CloudApiProperties;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Slf4j
@Service
@AllArgsConstructor
@SofaService(bindings = {@SofaServiceBinding(serialize = false)})
public class OAuthApiImpl implements OAuthApi {

    private final CloudApiProperties cloudApiProperties;
    private final EdgeCacheManager edgeCacheManager;

    private final static Object LOCK = new Object();

    public OAuthDto getOAuthInfo() {
        String requestPath = "/api/oauth.do";

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("type", "apidownload1");
        params.add("appid", cloudApiProperties.getInitUser());
        params.add("appsecret", cloudApiProperties.getInitPassword());

        ResponseEntity<OAuthDto> responseEntity = RequestUtils.doPostForm(cloudApiProperties.getSsoUrl() + requestPath, params, OAuthDto.class);
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            return responseEntity.getBody();
        } else {
            return null;
        }
    }

    @Override
    public String getAccessToken() {
        String token = edgeCacheManager.get(CacheKey.TOKEN, String.class);
        if (token == null) {
            synchronized (LOCK) {
                token = edgeCacheManager.get(CacheKey.TOKEN, String.class);
                if (token == null) {
                    OAuthDto oAuthDto = this.getOAuthInfo();
                    if (oAuthDto != null) {
                        token = oAuthDto.getMsg();
                        if (StrUtil.isBlank(token)) {
                            throw new InvalidTokenException(ErrorCode.ERROR401);
                        }
                        edgeCacheManager.set(CacheKey.TOKEN, token);
                        log.info("[Ehcache]写入: {}:{}", SpringContextUtils.getApplicationName(), CacheKey.TOKEN);
                    }
                }
            }

        }
        return token;
    }

    @Override
    public void removeAccessToken() {
        edgeCacheManager.remove(CacheKey.TOKEN);
    }
}

package com.mingdao.edge.core.api.biz.dto;

import com.mingdao.edge.core.common.constants.EdgeConstant;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * description BizInfo
 *
 * <AUTHOR>
 * @since 2022/7/12 19:08
 */
@Data
@NoArgsConstructor
public class BizInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    public BizInfo(String bizName, String version) {
        this.setBizName(bizName);
        this.setVersion(version);
    }

    public BizInfo(String bizName, String version, String downloadUrl) {
        this.setBizName(bizName);
        this.setVersion(version);
        this.setBizUrl(downloadUrl);
    }

    /**
     * 模组id
     */
    private Long bizId;

    private String bizName;

    private String bizUrl;

    private String version;

    private Map<String, Object> bizInitParams;

    public String getLocalPath() {
        return EdgeConstant.DOWNLOAD_PATH + bizName + "-" + version + "-ark-biz.jar";
    }
}

package com.mingdao.edge.plugin.gateway.service;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.mingdao.edge.core.api.biz.BizCache;
import com.mingdao.edge.core.common.cache.EdgeCacheManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * description BizCacheImpl
 *
 * <AUTHOR>
 * @since 2022/7/12 18:50
 */
@Slf4j
@Service
@SofaService
public class BizCacheImpl implements BizCache {

    private final static ConcurrentHashMap<String, ConcurrentHashMap<String, Object>> cache = new ConcurrentHashMap<>();

    @Resource
    private EdgeCacheManager edgeCacheManager;

    @Override
    public <T> T getInitParams(String bizName, String bizVersion, String cacheName) {
        String bizCacheKey = getBizCacheKey(bizName, bizVersion);
        return (T) edgeCacheManager.hGet(bizCacheKey, cacheName);
    }

    @Override
    public void setInitParams(String bizName, String bizVersion, String cacheName, Object data) {
        if (log.isDebugEnabled()) {
            log.debug("[DEBUG]SET CACHE: {}, {}, {}, {}", bizName, bizVersion, cacheName, JSONObject.toJSONString(data));
        }
        String bizCacheKey = getBizCacheKey(bizName, bizVersion);
        edgeCacheManager.hSet(bizCacheKey, cacheName, data);
    }

    public Map<String, ConcurrentHashMap<String, Object>> listAll() {
        return cache;
    }

    @Override
    public void remove(String firstLevelCacheKey) {
        if (StrUtil.isEmpty(firstLevelCacheKey)) {
            return;
        }
        cache.remove(firstLevelCacheKey);
    }

    @Override
    public void remove(String firstLevelCacheKey, String secondLevelCacheKey) {
        if (ArrayUtil.hasEmpty(firstLevelCacheKey, secondLevelCacheKey)) {
            return;
        }
        ConcurrentHashMap<String, Object> secondCache = cache.get(firstLevelCacheKey);
        if (Objects.isNull(secondCache)) {
            return;
        }
        secondCache.remove(secondLevelCacheKey);
    }

    @Override
    public <T> T getValue(String bizName, String bizVersion, String key, Class<T> clazz) {
        String bizCacheKey = getBizCacheKey(bizName, bizVersion);
        return edgeCacheManager.get(bizCacheKey + "_" + key, clazz);
    }

    @Override
    public <T> void setValue(String bizName, String bizVersion, String key, T value) {
        String bizCacheKey = getBizCacheKey(bizName, bizVersion);
        edgeCacheManager.set(bizCacheKey + "_" + key, value);
    }

    @Override
    public <T> void setValue(String bizName, String bizVersion, String key, T value, int timeout) {
        String bizCacheKey = getBizCacheKey(bizName, bizVersion);
        edgeCacheManager.set(bizCacheKey + "_" + key, value, timeout);
    }

    private String getBizCacheKey(String bizName, String bizVersion) {
        return bizName + "_" + bizVersion;
    }
}

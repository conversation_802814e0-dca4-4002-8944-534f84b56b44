package com.mingdao.edge.core.common.exception;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public class InvalidTokenException extends RuntimeException {

    private int code;
    private String msg;

    public InvalidTokenException(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
    public InvalidTokenException(ErrorCode errorCode) {
        this.code = errorCode.getCode();
        this.msg = errorCode.getMsg();
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

}

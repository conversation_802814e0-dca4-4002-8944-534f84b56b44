package com.mingdao.edge.plugin.data.source.driver.http;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * description DbDriverMSSQLApplication
 *
 * <AUTHOR>
 * @since 2025-06-06 17:17:39
 */
@EnableAsync
@SpringBootApplication(scanBasePackages = "com.mingdao.edge")
public class DbDriverHttpApplication {
    public static void main(String[] args) {
        SpringApplication.run(DbDriverHttpApplication.class, args);
    }
}

package com.mingdao.edge.plugin.data.sync.util;

import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
public class CronExpressionGenerator {

    /**
     * 生成固定分钟执行的Cron表达式
     * @param minute 分钟数(0-59)
     */
    public static String fixedMinute(int minute) {
        validateMinute(minute);
        return String.format("0 %d * * * ?", minute);
    }

    /**
     * 生成间隔分钟执行的Cron表达式
     * @param interval 间隔分钟数(1-59)
     */
    public static String intervalMinutes(int interval) {
        if (interval < 1 || interval > 59) {
            throw new IllegalArgumentException("Interval must be between 1 and 59");
        }
        return String.format("0 */%d * * * ?", interval);
    }

    /**
     * 生成多个指定分钟执行的Cron表达式
     * @param minutes 分钟数组(每个元素0-59)
     */
    public static String multipleMinutes(int... minutes) {
        String minutesStr = IntStream.of(minutes)
                .peek(CronExpressionGenerator::validateMinute)
                .distinct()
                .sorted()
                .mapToObj(String::valueOf)
                .collect(Collectors.joining(","));

        return String.format("0 %s * * * ?", minutesStr);
    }

    /**
     * 生成工作日(周一到周五)的固定时间Cron表达式
     * @param minute 分钟数(0-59)
     * @param hour 小时数(0-23)
     */
    public static String weekdayAt(int hour, int minute) {
        validateHour(hour);
        validateMinute(minute);
        return String.format("0 %d %d ? * MON-FRI", minute, hour);
    }

    private static void validateMinute(int minute) {
        if (minute < 0 || minute > 59) {
            throw new IllegalArgumentException("Minute must be between 0 and 59");
        }
    }

    private static void validateHour(int hour) {
        if (hour < 0 || hour > 23) {
            throw new IllegalArgumentException("Hour must be between 0 and 23");
        }
    }

    // 使用示例
    public static void main(String[] args) {
        System.out.println(fixedMinute(30));       // 0 30 * * * ?
        System.out.println(intervalMinutes(15));   // 0 */15 * * * ?
        System.out.println(multipleMinutes(5, 10, 15)); // 0 5,10,15 * * * ?
        System.out.println(weekdayAt(9, 30));      // 0 30 9 ? * MON-FRI
    }
}

package com.mingdao.edge.plugin.data.sync.config;

import com.alipay.sofa.runtime.api.annotation.SofaReference;
import com.mingdao.edge.core.common.biz.event.EventHandlerUtils;
import com.mingdao.edge.core.common.cache.EdgeCacheManager;
import com.mingdao.edge.core.pulsar.utils.PulsarClientUtil;
import com.mingdao.edge.plugin.api.gateway.ServerApi;
import com.mingdao.edge.plugin.api.gateway.dto.TenantMqttProperty;
import com.mingdao.edge.plugin.data.sync.listener.DataDownMessageListener;
import com.mingdao.edge.plugin.data.sync.service.impl.SyncTaskServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Slf4j
@Component
public class StartupApplicationListener {

    @Resource
    private EdgeCacheManager edgeCacheManager;
    @Resource
    private SyncTaskServiceImpl syncTaskServiceImpl;

    @SofaReference
    private ServerApi serverApi;

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {

        EventHandlerUtils.initEventHandler();

        TenantMqttProperty tenantMqttProperty = serverApi.getTenantMqttProperty();

        try {

            String url = tenantMqttProperty.getBroker();
            String clientName = "data-sync-" + tenantMqttProperty.getOrgCode();
            String authToken = tenantMqttProperty.getPassword();
            PulsarClientUtil.registerPulsarClient(url, clientName, authToken);

            String tenantName = "tenant-" + tenantMqttProperty.getOrgCode();
            new DataDownMessageListener(tenantName);

            syncTaskServiceImpl.init();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @PreDestroy
    public void beforeDestroy() {
        edgeCacheManager.clear();
    }
}

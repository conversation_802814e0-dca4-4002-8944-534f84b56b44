package com.mingdao.edge.plugin.data.sync.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.core.common.constants.RequestResult;
import com.mingdao.edge.core.common.dto.BaseResponse;
import com.mingdao.edge.plugin.api.data.sync.constants.DataState;
import com.mingdao.edge.plugin.api.data.sync.constants.MDColumn;
import com.mingdao.edge.plugin.api.data.sync.dto.ColTemp;
import com.mingdao.edge.plugin.api.gateway.dto.DataDto;
import com.mingdao.edge.plugin.api.gateway.dto.DownMessage;
import com.mingdao.edge.plugin.api.data.sync.dto.WarehouseProject;
import com.mingdao.edge.plugin.data.sync.base.service.impl.AbstractDataSyncDown;
import com.mingdao.edge.plugin.data.sync.base.utils.DownloadCloudTools;
import com.mingdao.edge.plugin.data.sync.service.YuzeApi;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Slf4j
public abstract class AbstractYuzeDataSyncDown extends AbstractDataSyncDown {

    @Resource
    protected YuzeApi yuzeApi;

    public AbstractYuzeDataSyncDown(String moduleName, String pageName, Integer pageSize) {
        super(moduleName, pageName, pageSize);
    }

    @Override
    public void getData() {
        String dtBasePage = taskInfo.getString("dt_basepage");
        BaseResponse<JSONArray> response = ewsApi.getWarehouseProjectList(dtBasePage, super.dtName, 0, null);
        if (response == null) {
            log.debug("[反写数据]{} 获取可写数据为空", className);
            return;
        }
        JSONArray dataJson = response.getData();
        if (dataJson != null && !dataJson.isEmpty()) {
            Integer limit = 20;
            for (int i = 0, size = dataJson.size(); i < size; i++) {
                JSONObject warehouseProjectJSON = dataJson.getJSONObject(i);
                saveWarehouseProject(warehouseProjectJSON);

                WarehouseProject warehouseProject = warehouseProjectJSON.toJavaObject(WarehouseProject.class);
                WAREHOUSE_PROJECT.put(warehouseProject.getAUTOID().toString(), warehouseProject);

                doGetCloudData(warehouseProject, 1, limit, fieldMap);
            }
        }
    }

    protected void doGetCloudData(WarehouseProject warehouseProject, Integer page, Integer pageSize, Map<String, ColTemp> fieldMap) {
        boolean goon = true;
        Long warehouseProjectId = warehouseProject.getAUTOID();
        while (goon) {
            try {
                JSONArray dataList = getPushDataList(warehouseProject, page, pageSize);
                page++;
                if (dataList != null) {
                    if (!dataList.isEmpty()) {
                        for (int index = 0; index < dataList.size(); index++) {
                            JSONObject dataObj = ((JSONObject) dataList.get(index));
                            dataObj.put(MDColumn.MD_WAREHOUSE_PROJECT_ID, warehouseProjectId);
                            processData(dataObj);
                        }
                        //if (warehouseProject.getInteger("HISTORYSTATUS") == 0) {
                        //    JSONObject updateHistoryStatusObj = new JSONObject();
                        //    updateHistoryStatusObj.put("AUTOID", warehouseProject.getString("AUTOID"));
                        //    ewsApi.updateHistoryStatus(updateHistoryStatusObj);
                        //}
                    }
                    if (Objects.equals(pageSize, dataList.size())) {
                        doGetCloudData(warehouseProject, page, pageSize, fieldMap);
                    }
                }
                goon = false;
            } catch (Exception e) {
                log.error(StrUtil.format("[获取云端数据][{}]异常: {}", className, e.getMessage()), e);
            } finally {
                log.info("[获取云端数据][{}]结束", className);
            }
        }
    }

    @Override
    protected DownMessage<?> doTransDownData(JSONObject dataObj) {
        DownMessage<DataDto<Object>> downMessage = new DownMessage<>();
        Long id = dataObj.getLong(MDColumn.ID);

        DataDto<Object> dataDto = new DataDto<>();
        dataDto.setId(id.toString());
        dataDto.setDataTypeName(className);

        String warehouseProjectId = dataObj.getString(MDColumn.MD_WAREHOUSE_PROJECT_ID);
        JSONObject warehouseProjectJSON = getWarehouseProject(warehouseProjectId);
        WarehouseProject warehouseProject = warehouseProjectJSON.toJavaObject(WarehouseProject.class);

        JSONObject data = DownloadCloudTools.convertDataToCustomerDataType(dataObj, fieldMap);

        JSONObject updateObj = new JSONObject();
        String autoId = dataObj.getString("autoid");
        updateObj.put("AUTOID", autoId);
        try {
            JSONObject info = getData(data);
            if (info.getInteger("code") == 0) {
                // 有数据， 修改
                //JSONObject data = info.getJSONObject("data");
                JSONObject dataItem = warehouseProject.convertToUserData(data, fieldMap);
                JSONObject saveResult = editData(dataItem);
                setUpdateObjectSuccess(warehouseProjectId, id, updateObj, dataObj, saveResult);
                downMessage.setCode(RequestResult.RES_SUCCESS);
            } else {
                // 无数据，新增
                JSONObject dataItem = warehouseProject.convertToUserData(data, fieldMap);
                if (!dataItem.isEmpty()) {
                    JSONObject saveResult = addData(dataItem);
                    if (saveResult.getInteger("code") == 200) {
                        // 保存成功
                        setUpdateObjectSuccess(warehouseProjectId, id, updateObj, dataObj, saveResult);
                        downMessage.setCode(RequestResult.RES_SUCCESS);
                    } else {
                        setUpdateObjectFail(warehouseProjectId, id, updateObj, saveResult.getString("msg"));
                        downMessage.setCode(RequestResult.RES_FAIL);
                    }
                } else {
                    log.warn("数据反写失败: 转换后的数据为空");
                    setUpdateObjectFail(warehouseProjectId, id, updateObj, "数据反写失败: 转换后的数据为空");
                    downMessage.setCode(RequestResult.RES_FAIL);
                }
            }
            updateCloudStatus(updateObj);
        } catch (Exception e) {
            log.error("获取云端数据异常: {}", e.getMessage(), e);
        }
        dataDto.setData(updateObj);
        downMessage.setData(dataDto);
        return downMessage;
    }

    @Override
    public JSONObject getPraseData(Long id) {
        Map<String, Object> item = getSavedData(id);
        if (item == null) {
            log.warn("[转换客户数据][{}]重复数据", className);
            return null;
        }

        updateState(item.get("id"), DataState.WAITING_UPLOAD);

        return new JSONObject(item);
    }
}

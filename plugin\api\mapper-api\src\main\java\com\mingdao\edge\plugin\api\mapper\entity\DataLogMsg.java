package com.mingdao.edge.plugin.api.mapper.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@TableName("tb_data_log_msg")
@Entity(name = "tb_data_log_msg")
public class DataLogMsg {

    @Id
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long logId;
    private String msg;
    private Long createTime;
}

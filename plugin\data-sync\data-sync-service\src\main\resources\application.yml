app.version: ${project.version}

server:
  port: ${data_sync_port:9203}

spring:
  application:
    name: ${project.name}
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration

logging:
  file:
    path: ./logs/${project.name}

mingdao:
  check_fail_data_interval: 3600000
  edge:
    cache:
      type: ehcache
      ehcache:
        cacheName: ${spring.application.name}_${project.version}
        offheap: 1000
        disk: 10000

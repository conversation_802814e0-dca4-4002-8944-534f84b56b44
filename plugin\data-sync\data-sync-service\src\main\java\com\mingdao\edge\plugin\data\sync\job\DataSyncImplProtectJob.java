package com.mingdao.edge.plugin.data.sync.job;

import com.mingdao.edge.plugin.data.sync.service.impl.SyncTaskServiceImpl;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 守护数据同步biz
 * 重启时可能会丢失跨biz的通讯接口
 * 由这里来重启
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Component
public class DataSyncImplProtectJob {

    @Resource
    private SyncTaskServiceImpl syncTaskService;

    @Scheduled(fixedRate = 30000, initialDelay = 120000)
    public void checkData() {
        syncTaskService.init();
    }

}

package com.mingdao.edge.core.pulsar.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "pulsar.consumer")
public class ConsumerProperty {
    /**
     * 默认订阅名称
     */
    private String defaultSubscriptionName = "default-subscription";

    /**
     * 订阅类型
     */
    private String subscriptionType = "Shared";

    /**
     * 接收队列大小
     */
    private int receiverQueueSize = 1000;

    /**
     * 确认超时时间（毫秒）
     */
    private long ackTimeoutMs = 30000;

    /**
     * 负确认重新投递延迟（毫秒）
     */
    private long negativeAckRedeliveryDelayMs = 60000;

    /**
     * 最大重新投递次数
     */
    private int maxRedeliverCount = 3;

    /**
     * 死信主题
     */
    private String deadLetterTopic;

    /**
     * 重试主题
     */
    private String retryTopic;

    /**
     * 是否启用批量接收
     */
    private boolean batchReceiveEnabled = false;

    /**
     * 批量接收最大消息数
     */
    private int batchReceiveMaxMessages = 100;

    /**
     * 批量接收超时时间（毫秒）
     */
    private long batchReceiveTimeoutMs = 100;

    /**
     * 并行消费的工作线程数（>1 时启用并行处理）
     */
    private int concurrency = 4;

    /**
     * 并行处理队列容量（用于限流与背压）
     */
    private int queueCapacity = 1000;
}

# Pulsar 工具包

这是一个基于 Apache Pulsar 4.0.6 的工具包，提供了运行时动态创建连接、订阅和发送消息的功能。

## 功能特性

1. **运行时动态创建连接和订阅**：支持在应用运行时动态创建 Pulsar 客户端连接和消息订阅
2. **消息发送**：提供同步、异步和延迟消息发送功能
3. **Spring 集成**：与 Spring 框架深度集成，支持依赖注入和 Bean 管理
4. **消息监听**：提供抽象的消息监听器接口，便于扩展

## 核心组件

### 1. PulsarClientInfo
客户端配置类，用于配置 Pulsar 连接参数和订阅信息。

### 2. PulsarService
消息发送服务，提供各种消息发送方法。

### 3. PulsarMessageListener
消息监听器接口，用于处理接收到的消息。

### 4. RegisterPulsarClientUtil
工具类，用于注册和管理 Pulsar 客户端。

## 使用示例

### 1. 基本使用

```java
@Component
public class PulsarExample {
    
    @Resource
    private PulsarService pulsarService;
    
    // 发送消息
    public void sendMessage() {
        pulsarService.send("my-topic", "Hello Pulsar!");
    }
    
    // 异步发送消息
    public void sendAsyncMessage() {
        pulsarService.sendAsync("my-topic", "Hello Async!")
            .whenComplete((messageId, throwable) -> {
                if (throwable != null) {
                    log.error("发送失败", throwable);
                } else {
                    log.info("发送成功: {}", messageId);
                }
            });
    }
}
```

### 2. 创建消息监听器

```java
@Component
public class MyPulsarMessageListener extends AbstractPulsarMessageListener {
    
    @Override
    protected void handleMessage(String topic, String messageId, byte[] payload, 
                                String payloadStr, Message<byte[]> message) {
        log.info("收到消息: topic={}, content={}", topic, payloadStr);
        
        // 处理业务逻辑
        processBusinessLogic(payloadStr);
    }
    
    private void processBusinessLogic(String message) {
        // 实现具体的业务逻辑
    }
}
```

### 3. 在应用启动时注册客户端

```java
@Component
public class PulsarStartupListener {
    
    @Resource
    private MyPulsarMessageListener messageListener;
    
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        try {
            // 方式1：简单注册
            RegisterPulsarClientUtil.registerPulsarClient(
                "pulsar://localhost:6650",
                "my-client",
                Arrays.asList("topic1", "topic2"),
                "my-subscription",
                messageListener
            );
            
            // 方式2：使用构建器
            RegisterPulsarClientUtil.builder("pulsar://localhost:6650")
                .clientName("my-client")
                .authToken("your-auth-token")
                .addSubscription("topic1", "subscription1", SubscriptionType.Shared, messageListener)
                .addSubscription("topic2", "subscription2", SubscriptionType.Exclusive, messageListener)
                .register();
                
        } catch (Exception e) {
            log.error("注册Pulsar客户端失败", e);
        }
    }
}
```

### 4. 高级配置

```java
// 创建完整配置
PulsarClientInfo clientInfo = new PulsarClientInfo();
clientInfo.setServiceUrl("pulsar://localhost:6650");
clientInfo.setClientName("advanced-client");
clientInfo.setAuthToken("your-token");
clientInfo.setConnectionTimeoutSeconds(60);
clientInfo.setOperationTimeoutSeconds(60);
clientInfo.setTlsEnabled(true);

// 创建订阅配置
List<PulsarClientInfo.PulsarSubscriptionConfig> subscriptions = new ArrayList<>();
PulsarClientInfo.PulsarSubscriptionConfig config = new PulsarClientInfo.PulsarSubscriptionConfig();
config.setTopic("my-topic");
config.setSubscriptionName("my-subscription");
config.setSubscriptionType(SubscriptionType.Shared);
config.setReceiverQueueSize(2000);
config.setMessageListener(messageListener);
subscriptions.add(config);

clientInfo.setSubscriptions(subscriptions);

// 注册客户端
RegisterPulsarClientUtil.registerPulsarClient(clientInfo);
```

## 依赖配置

在 `pom.xml` 中添加依赖：

```xml
<dependency>
    <groupId>com.mingdao.edge.core</groupId>
    <artifactId>common-pulsar</artifactId>
    <version>${project.version}</version>
</dependency>
```

## 注意事项

1. **Spring 管理**：由于支持动态创建连接，Pulsar 客户端、生产者和消费者都通过 Spring 容器进行管理
2. **线程安全**：所有组件都是线程安全的，可以在多线程环境中使用
3. **资源管理**：客户端会自动管理连接和资源，无需手动关闭
4. **错误处理**：提供了完善的错误处理和日志记录机制

## 常量定义

`PulsarConstants` 类提供了常用的常量定义，包括：
- 默认配置参数
- Bean 名称前缀
- 主题名称前缀
- 消息属性键

使用这些常量可以保持代码的一致性和可维护性。

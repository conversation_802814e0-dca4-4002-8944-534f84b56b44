package com.mingdao.edge.plugin.gateway.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.alipay.sofa.runtime.api.annotation.SofaServiceBinding;
import com.mingdao.edge.core.api.biz.BizExchangeService;
import com.mingdao.edge.core.api.biz.GatewayDataExchanger;
import com.mingdao.edge.core.api.biz.dto.BaseEvent;
import com.mingdao.edge.core.api.biz.dto.BizMessage;
import com.mingdao.edge.plugin.gateway.service.ReferenceClientService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * description GatewayDataExchangerImpl
 *
 * <AUTHOR>
 * @since 2022/7/19 20:13
 */
@Service
@SuppressWarnings({"rawtypes", "unchecked"})
@SofaService(bindings = {@SofaServiceBinding(serialize = false)})
public class GatewayDataExchangerImpl implements GatewayDataExchanger {

    @Resource
    ReferenceClientService referenceClientService;

    @Override
    public void exchange(BizMessage<BaseEvent<?>> messageDTO) {
        String uniqueId = messageDTO.getTargetBiz();
        if (StrUtil.isNotBlank(uniqueId)) {
            BizExchangeService bizExchangeService = referenceClientService.getService(uniqueId, BizExchangeService.class);
            if (bizExchangeService != null) {
                bizExchangeService.handler(messageDTO);
            }
        }
    }
}

package com.mingdao.edge.core.common.http;

import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.Map;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public class ParamsTools {
    public static MultiValueMap<String, Object> getFormDataMap(Map<String, Object> params) {
        MultiValueMap<String, Object> paramsMap = new LinkedMultiValueMap<>();
        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                paramsMap.add(entry.getKey(), entry.getValue());
            }
        }
        return paramsMap;
    }
}

package com.mingdao.edge.core.common.service;

import cn.hutool.core.util.StrUtil;
import com.alipay.sofa.runtime.api.annotation.SofaClientFactory;
import com.alipay.sofa.runtime.api.client.ReferenceClient;
import com.alipay.sofa.runtime.api.client.param.ReferenceParam;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Slf4j
public abstract class AbstractReferenceServiceHolder<T> {

    protected ConcurrentHashMap<String, T> servicesMap = new ConcurrentHashMap<>();

    @SofaClientFactory
    private ReferenceClient referenceClient;

    public boolean containsService(String uniqueId) {
        return servicesMap.containsKey(uniqueId);
    }

    public T getService(String uniqueId, Class<T> clazz) {
        T t;
        if(servicesMap.containsKey(uniqueId)) {
            t = servicesMap.get(uniqueId);
        }
        else {
            try {
                ReferenceParam<T> referenceParam = new ReferenceParam<>();
                referenceParam.setInterfaceType(clazz);
                referenceParam.setUniqueId(uniqueId);
                t = referenceClient.reference(referenceParam);
                log.info("新增 {} 服务: {}", this.getClass().getSimpleName(), uniqueId);
                servicesMap.put(uniqueId, t);
            } catch (Exception e) {
                t = null;
                log.error(StrUtil.format("注册 {} 异常: {}", this.getClass().getSimpleName(), e.getMessage(), e));
            }
        }
        return t;
    }

    public List<T> getAllService() {
        return new ArrayList<>(servicesMap.values());
    }

    public void removeService(String uniqueId) {
        servicesMap.remove(uniqueId);
        log.info("移除服务: {} {}", this.getClass().getSimpleName(), uniqueId);
    }

    public T getRawService(String uniqueId, Class<T> clazz) {
        T t;
        try {
            ReferenceParam<T> referenceParam = new ReferenceParam<>();
            referenceParam.setInterfaceType(clazz);
            referenceParam.setUniqueId(uniqueId);
            t = referenceClient.reference(referenceParam);
        } catch (Exception e) {
            t = null;
            log.error(StrUtil.format("注册 {} 异常: {}", this.getClass().getSimpleName(), e.getMessage(), e));
        }
        return t;
    }
}

package com.mingdao.edge.plugin.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mingdao.edge.plugin.api.mapper.entity.DataLog;
import org.apache.ibatis.annotations.*;

@Mapper
public interface DataLogMapper extends BaseMapper<DataLog> {

    @Insert("INSERT INTO tb_data_log (sys_org, biz_type, biz_id, data_id, MD_STATE, MD_DT_NAME, create_time, update_time) VALUES " +
            "(#{sysOrg}, #{bizType}, #{bizId}, #{dataId}, #{MD_STATE}, #{MD_DT_NAME}, #{createTime}, #{updateTime})")
    int add(DataLog recordInfo);

    @Delete("DELETE FROM user WHERE createTime < '${beforeTime}'")
    int deleteBefore(String beforeTime);

    @Select("select * FROM tb_data_log where data_id = #{id} and biz_type = #{bizType} and biz_id = #{bizId}")
    DataLog selectByDataId(@Param("id") Long id, @Param("bizType") String bizType, @Param("bizId") String bizId);
}

package com.mingdao.edge.plugin.data.sync.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.plugin.data.sync.base.service.SyncTaskService;
import com.mingdao.edge.plugin.data.sync.context.TaskContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Slf4j
@Service
public class SyncTaskServiceImpl implements SyncTaskService {

    @Resource
    private CacheService cacheService;
    @Resource
    private TaskContextHolder taskContextHolder;

    public void init() {
        JSONArray taskList = cacheService.getTaskInfo(false);
        if (!CollectionUtil.isEmpty(taskList)) {
            for (int i = 0; i < taskList.size(); i++) {
                JSONObject taskInfo = taskList.getJSONObject(i);
                taskContextHolder.createTaskContext(taskInfo);
            }
        }
    }

    public void removeTaskByUnique(String uniqueId) {
        taskContextHolder.removeTaskContext(uniqueId);
    }

}

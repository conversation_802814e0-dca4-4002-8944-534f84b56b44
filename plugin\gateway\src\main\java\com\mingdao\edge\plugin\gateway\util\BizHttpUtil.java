package com.mingdao.edge.plugin.gateway.util;


import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpUtil;
import com.mingdao.edge.core.common.constants.EdgeConstant;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc desc
 */
@Slf4j
public class BizHttpUtil {


    /**
     * 默认超时时间: 10s
     */
    private static final int TIME_OUT = 1000 * 10;

    static {
        if (!FileUtil.exist(EdgeConstant.DOWNLOAD_PATH)) {
            // 如果下载目录不存在就创建
            FileUtil.mkdir(EdgeConstant.DOWNLOAD_PATH);
        }
    }

    /**
     * 使用默认超时时间下载bizJar包，默认10s超时
     * @param downloadUrl
     * @param fileName
     * @param version
     * @return
     */
    public static Optional<String> downLoad(String downloadUrl, String path) {
        return downLoad(downloadUrl, path, TIME_OUT);
    }

    /**
     * 下载指定url的jar包
     *
     * @param downloadUrl
     * @return 返回下载的这个文件的全路径
     */
    public static Optional<String> downLoad(String downloadUrl, String filePath, int timeOut) {

        File bizFile = new File(filePath);
        if (bizFile.exists()) {
            log.info("文件：{} 已存在，不再下载", filePath);
            return Optional.of(bizFile.getAbsolutePath());
        }

        log.info("开始下载：{}", filePath);
        long size = 0;
        try {
            size = HttpUtil.downloadFile(downloadUrl, bizFile, timeOut);
        } catch (Exception e) {
            log.error("文件下载失败：", e);
            return Optional.empty();
        }
        log.info("下载完成：{}， 大小：{} byte", filePath, size);
        return Optional.of(filePath);
    }



}

package com.mingdao.edge.plugin.data.sync.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.plugin.api.data.sync.constants.DataState;
import com.mingdao.edge.plugin.api.data.sync.dto.ColTemp;
import com.mingdao.edge.plugin.data.sync.base.service.impl.AbstractDataSyncUp;
import com.mingdao.edge.plugin.data.sync.service.YuzeApi;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Slf4j
public abstract class AbstractYuzeDataSyncUp extends AbstractDataSyncUp {

    @Resource
    protected YuzeApi yuzeApi;

    public AbstractYuzeDataSyncUp(String customerName, String moduleName, Integer pageSize) {
        super(customerName, moduleName, pageSize);
    }

    @Override
    public void getData() {
        Integer page = 1;
        Long dto_uptime = getLastDataTime();
        log.debug("[获取客户数据][{}]查询时间: {}", className, dto_uptime);
        long newQueryStartTime = System.currentTimeMillis();
        doGetUserData(url, dto_uptime, dtName, page, pageSize, fieldMap);
        updateLastTime(newQueryStartTime);
    }

    protected void doGetUserData(String url, Long dtoUptime, String dtName, Integer page, Integer pageSize, Map<String, ColTemp> fieldMap) {
        boolean goon = true;
        while (goon) {
            try {
                String queryTimeString = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_FORMATTER);
                JSONObject userData = yuzeApi.getUserDataFromApi(url, dtoUptime, page, pageSize);
                if (userData != null && userData.containsKey("data")) {
                    //log.debug("[获取客户数据][yuze]获取成功: {}", userData);
                    // {"msg":"成功","code":200,"data":{"pageNum":1,"pageSize":1000,"size":0,"total":0,"pages":0,"list":[]}}
                    //disruptorApi.publish(tt);
                    //dataLogService.addDataLog(1L, "ss", "123", "待同步", System.currentTimeMillis());
                    goon = false;
                    JSONObject data = userData.getJSONObject("data");
                    JSONArray dataList = data.getJSONArray("list");
                    if (!CollectionUtil.isEmpty(dataList)) {
                        log.info("[获取客户数据][{}]数量: {}, page: {}", className, dataList.size(), page);
                        for (int i = 0; i < dataList.size(); i++) {
                            JSONObject item = dataList.getJSONObject(i);
                            processData(item);
                        }
                        Integer size = data.getInteger("size");
                        Integer returnPageSize = data.getInteger("pageSize");
                        if (Objects.equals(size, returnPageSize)) {
                            page++;
                            doGetUserData(url, dtoUptime, dtName, page, pageSize, fieldMap);
                        }
                    }
                }
            } catch (Exception e) {
                log.error(StrUtil.format("[获取客户数据][{}]异常: {}", className, e.getMessage()), e);
                goon = false;
            } finally {
                log.info("[获取客户数据][{}]结束", className);
            }
        }
    }

    @Override
    public JSONObject getPraseData(Long id) {
        Map<String, Object> item = getSavedData(id);
        if (item == null) {
            log.warn("[转换客户数据][{}]重复数据", className);
            return null;
        }

        updateState(item.get("id"), DataState.WAITING_UPLOAD);

        return new JSONObject(item);
    }

    @Override
    public boolean convertSpecField(String fieldName, Object value, Map<String, Object> result) {
        return false;
    }
}

package com.mingdao.edge.plugin.data.source.driver.mssql.holder;

import com.mingdao.edge.plugin.data.source.driver.api.dto.MSSQLProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * MSSQL配置参数单例持有者
 * 简单的单例模式，用于全局管理MSSQL连接配置
 * 只在初始化时设置一次，后续只读取，无需复杂的线程安全机制
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Component
public class MSSQLPropertyHolder {

    /**
     * MSSQL配置实例，只在初始化时设置一次
     */
    private static MSSQLProperty mssqlProperty;

    /**
     * 私有构造函数，防止外部实例化
     */
    private MSSQLPropertyHolder() {
    }

    /**
     * 获取MSSQL配置实例
     *
     * @return MSSQL配置实例，如果未初始化则返回null
     */
    public static MSSQLProperty getInstance() {
        return mssqlProperty;
    }

    /**
     * 设置MSSQL配置实例
     * 只在初始化时调用一次
     *
     * @param property MSSQL配置实例
     */
    public static void setInstance(MSSQLProperty property) {
        if (property != null) {
            // 确保URL已构建
            property.buildUrl();
            log.info("MSSQL property set: host={}, database={}",
                    property.getMssqlHost(), property.getDataBaseName());
        } else {
            log.warn("Setting MSSQL property to null");
        }
        mssqlProperty = property;
    }

    /**
     * 检查是否已初始化
     *
     * @return true if initialized, false otherwise
     */
    public static boolean isInitialized() {
        return mssqlProperty != null;
    }

    /**
     * 获取数据库名称
     * 便捷方法，避免空指针异常
     *
     * @return 数据库名称，如果未初始化则返回null
     */
    public static String getDatabaseName() {
        return mssqlProperty != null ? mssqlProperty.getDataBaseName() : null;
    }

    /**
     * 获取主机地址
     * 便捷方法，避免空指针异常
     *
     * @return 主机地址，如果未初始化则返回null
     */
    public static String getHost() {
        return mssqlProperty != null ? mssqlProperty.getMssqlHost() : null;
    }

    /**
     * 获取连接URL
     * 便捷方法，避免空指针异常
     *
     * @return 连接URL，如果未初始化则返回null
     */
    public static String getUrl() {
        return mssqlProperty != null ? mssqlProperty.getUrl() : null;
    }

    /**
     * 获取用户名
     * 便捷方法，避免空指针异常
     *
     * @return 用户名，如果未初始化则返回null
     */
    public static String getUserName() {
        return mssqlProperty != null ? mssqlProperty.getUserName() : null;
    }

    /**
     * 清除配置实例
     * 主要用于测试或重新初始化场景
     */
    public static void clear() {
        log.info("Clearing MSSQL property instance");
        mssqlProperty = null;
    }

    /**
     * 获取配置的副本
     * 返回配置的深拷贝，避免外部修改影响单例
     *
     * @return 配置副本，如果未初始化则返回null
     */
    public static MSSQLProperty getCopy() {
        if (mssqlProperty == null) {
            return null;
        }

        MSSQLProperty copy = new MSSQLProperty();
        copy.setMssqlHost(mssqlProperty.getMssqlHost());
        copy.setDataBaseName(mssqlProperty.getDataBaseName());
        copy.setUserName(mssqlProperty.getUserName());
        copy.setPassword(mssqlProperty.getPassword());
        copy.setUrl(mssqlProperty.getUrl());
        return copy;
    }

    /**
     * 获取配置信息字符串
     * 用于调试和状态显示
     *
     * @return 配置信息字符串，如果未初始化则返回"Not initialized"
     */
    public static String getConfigInfo() {
        if (mssqlProperty == null) {
            return "MSSQL Property: Not initialized";
        }

        return String.format("MSSQL Property: host=%s, database=%s, user=%s, url=%s",
                mssqlProperty.getMssqlHost(),
                mssqlProperty.getDataBaseName(),
                mssqlProperty.getUserName(),
                mssqlProperty.getUrl());
    }


}

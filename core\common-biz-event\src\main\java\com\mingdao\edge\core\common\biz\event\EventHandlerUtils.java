package com.mingdao.edge.core.common.biz.event;

import com.mingdao.edge.core.api.biz.dto.BaseEvent;
import com.mingdao.edge.core.common.context.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;

import java.util.Map;

/**
 * description EventBusUtils
 *
 * <AUTHOR>
 * @since 2022/2/15 16:20
 */
@Slf4j
@SuppressWarnings("all")
public class EventHandlerUtils {
    /**
     * 系统初始化时，会写入，线程安全
     */
    public static Map<String, IBizEventHandler> EVENT_BUS_HANDLER = null;

    /**
     * 初始化事件处理器
     */
    public static void initEventHandler() {
        try {
            Map<String, IBizEventHandler> map = SpringContextUtils.getBeansOfType(IBizEventHandler.class);
            if(map != null && !map.isEmpty()) {
                map.keySet().forEach(a-> log.info("已加载事件处理器:{}", a));
            }
            EventHandlerUtils.EVENT_BUS_HANDLER = map;
        }
        catch (BeansException e) {
            log.error("初始化事件总线处理器出现异常", e);
        }
    }

    public static <T extends BaseEvent> IBizEventHandler<T> getEventBusHandler(String name) {
        if (EVENT_BUS_HANDLER == null) {
            return null;
        }
        return EVENT_BUS_HANDLER.get(name);
    }

    public static void fireEvent(BaseEvent data) {
        String eventType = data.getEventHandlerName();
        IBizEventHandler<BaseEvent> bizEventHandler = getEventBusHandler(eventType);
        if(bizEventHandler == null) {
            throw new RuntimeException("找不到事件" + eventType);
        }
        bizEventHandler.process(data);
    }
}

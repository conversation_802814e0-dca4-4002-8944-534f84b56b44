package com.mingdao.edge.plugin.gateway.controller;

import com.alipay.sofa.ark.api.ArkClient;
import com.mingdao.edge.core.api.biz.EdgeGatewayService;
import com.mingdao.edge.core.api.biz.GatewayDataExchanger;
import com.mingdao.edge.core.api.biz.dto.BizInfo;
import com.mingdao.edge.core.common.context.SpringContextUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.PulsarClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/biz")
public class CommandController {

    @Resource
    GatewayDataExchanger gatewayDataExchanger;
    @Resource
    EdgeGatewayService edgeGatewayService;

    @SneakyThrows
    @PostMapping("/install")
    @ResponseBody
    public Object sendCommand(@RequestBody Map<String, Object> map) {
        String bizName = map.get("biz").toString();
        String version = map.get("version").toString();
        BizInfo bizInfo = new BizInfo(bizName, version);
        edgeGatewayService.installBiz(bizInfo);
        return "0";
    }

    @GetMapping("/pulsar/test")
    @ResponseBody
    public Object testPulsar() {
        PulsarClient pulsarClient = SpringContextUtils.getBean(PulsarClient.class);
        //String tenantName = "tenant-140";
        //
        //JSONObject param = new JSONObject();
        //param.put("bizName", "");
        //param.put("version", "1.0.0");
        //param.put("downloadUrl", "");
        //
        //DownMessage<CommandDto<Map<String, Object>>> downMessage = DownMessage.buildDownMessage("restart_biz", params);
        //String topic = PulsarClientUtil.getFullTopic(tenantName,PulsarNamespace.CMD, "restart");
        //PulsarClientUtil.send(topic,);
        return pulsarClient.isClosed();
    }

    @GetMapping
    @ResponseBody
    public Object getAllBizNames() {
        //BizMessageDTO messageDTO = new BizMessageDTO();
        //messageDTO.setSourceBiz(BizUnique.DATA_CONVERTOR);
        //messageDTO.setDeviceKey("");
        //messageDTO.setSourceUniqueId("edge-gateway");
        //messageDTO.setTargetBiz(BizUnique.DATA_COLLECTOR);

        //BaseEventDTO eventDTO = new BaseEventDTO(EventTypeConstants.DATA_UP, null);
        //messageDTO.setData(eventDTO);
        //
        //gatewayDataExchanger.exchange(messageDTO);
        //return ArkClient.getBizManagerService().getAllBizNames();
        return "0";
    }

    @GetMapping("/check/{bizName}")
    @ResponseBody
    public Object checkBiz(@PathVariable String bizName) {
        return ArkClient.checkBiz(bizName);
        //return "0";
    }

    @GetMapping("/{bizName}")
    @ResponseBody
    public Object getAllBizNames(@PathVariable String bizName) {
        return ArkClient.getBizManagerService().getBiz(bizName);
        //return "0";
    }

    @PostMapping("/uninstall")
    @ResponseBody
    public Object uninstall(@RequestBody Map<String, Object> map) throws Throwable {
        String biz = map.get("biz").toString();
        String version = map.get("version").toString();
        ArkClient.uninstallBiz(biz, version);
        return "0";
    }

}

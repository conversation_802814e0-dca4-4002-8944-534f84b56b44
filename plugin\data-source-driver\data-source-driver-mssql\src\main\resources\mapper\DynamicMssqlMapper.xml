<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mingdao.edge.plugin.data.source.driver.mssql.mapper.DynamicMssqlMapper">

    <!-- 执行查询SQL -->
    <select id="selectBySql" resultType="java.util.Map">
        ${sql}
    </select>

    <!-- 执行分页查询SQL -->
    <select id="selectPageBySql" resultType="java.util.Map">
        ${sql}
    </select>

    <!-- 执行插入SQL -->
    <insert id="insertBySql" useGeneratedKeys="true" keyProperty="params.id">
        ${sql}
    </insert>

    <!-- 执行更新SQL -->
    <update id="updateBySql">
        ${sql}
    </update>

    <!-- 执行删除SQL -->
    <delete id="deleteBySql">
        ${sql}
    </delete>

    <!-- 根据ID列表批量删除 -->
    <delete id="deleteByIds">
        DELETE FROM ${tableName}
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 执行COUNT查询 -->
    <select id="countBySql" resultType="long">
        ${sql}
    </select>

    <!-- 检查表是否存在 -->
    <select id="checkTableExists" resultType="int">
        SELECT COUNT(*)
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME = #{tableName}
        AND TABLE_TYPE = 'BASE TABLE'
    </select>

    <!-- 获取表结构信息 -->
    <select id="getTableStructure" resultType="java.util.Map">
        SELECT
            COLUMN_NAME as columnName,
            DATA_TYPE as dataType,
            CHARACTER_MAXIMUM_LENGTH as maxLength,
            IS_NULLABLE as isNullable,
            COLUMN_DEFAULT as defaultValue,
            ORDINAL_POSITION as position
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = #{tableName}
        ORDER BY ORDINAL_POSITION
    </select>

</mapper>

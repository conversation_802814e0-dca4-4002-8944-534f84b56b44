package com.mingdao.edge.plugin.data.source.driver.api.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.springframework.http.HttpMethod;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
public class BasePageQueryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer page;
    private Integer size;
    private JSONObject params;
    private JSONObject header;

    /**
     * 该参数仅用于传递，不用于查询参数
     */
    private JSONObject exParams;
}

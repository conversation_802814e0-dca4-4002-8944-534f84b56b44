package com.mingdao.edge.core.common.exception;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.mingdao.edge.core.common.dto.HttpResponseStatus;
import com.mingdao.edge.core.common.dto.IResponseStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Optional;

/**
 * 应用异常
 */
@Setter
@Getter
@NoArgsConstructor
public class ApplicationException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 响应状态码
     */
    private int code = HttpResponseStatus.ERROR.code();

    /**
     * 异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     */
    private String msg = HttpResponseStatus.ERROR.msg();

    /**
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     */
    public ApplicationException(String msg, Object... msgParams) {
        this(HttpResponseStatus.ERROR, null, msg, msgParams);
    }

    /**
     * @param cause 原异常
     */
    public ApplicationException(Throwable cause) {
        this(HttpResponseStatus.ERROR, cause, null);
    }

    /**
     * @param cause     原异常
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     */
    public ApplicationException(Throwable cause, String msg, Object... msgParams) {
        this(HttpResponseStatus.ERROR, cause, msg, msgParams);
    }

    /**
     * @param status 响应状态
     */
    public ApplicationException(IResponseStatus status) {
        this(status, (String) null);
    }

    /**
     * @param status 响应状态
     * @param cause  原异常
     */
    public ApplicationException(IResponseStatus status, Throwable cause) {
        this(status, cause, null);
    }

    /**
     * @param status    响应状态
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     */
    public ApplicationException(IResponseStatus status, String msg, Object... msgParams) {
        this(status, null, msg, msgParams);
    }

    /**
     * @param status    响应状态
     * @param cause     原异常
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     */
    public ApplicationException(IResponseStatus status, Throwable cause, String msg, Object... msgParams) {
        this(cause, Optional.ofNullable(status).map(IResponseStatus::code).orElse(HttpResponseStatus.ERROR.code()), msg, msgParams);
    }

    /**
     * @param cause     原异常
     * @param code      响应状态码
     * @param msg       异常信息（可以用占位符 {}，结合信息参数msgParams使用）
     * @param msgParams 异常信息参数（替换msg中的占位符 {}）
     */
    private ApplicationException(Throwable cause, int code, String msg, Object... msgParams) {
        super(CharSequenceUtil.format(StrUtil.blankToDefault(msg, ""), msgParams), cause);
        this.code = code;
        this.msg = super.getMessage();
    }
}

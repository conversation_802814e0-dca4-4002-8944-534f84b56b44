package com.mingdao.edge.core.api.biz.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * biz之间调用的DTO
 *
 * <AUTHOR>
 * @since 2022/7/19 19:32
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BizMessage<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private String sourceBiz;
    private String targetBiz;
    private BaseEvent<T> data;
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.mingdao</groupId>
        <artifactId>edge</artifactId>
        <version>${revision}</version>
    </parent>

    <groupId>com.mingdao.edge</groupId>
    <artifactId>plugin</artifactId>
    <packaging>pom</packaging>

    <properties>
        <cloud.version>1.0.0</cloud.version>
    </properties>

    <modules>
        <module>api</module>
        <module>gateway</module>
        <module>data-source-driver</module>
        <module>data-sync</module>
        <module>cloud</module>
        <!--<module>third-part-api</module>-->
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.mingdao.edge.core</groupId>
                <artifactId>biz-api</artifactId>
                <version>${biz-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mingdao.edge.core</groupId>
                <artifactId>ark-event</artifactId>
                <version>${ark-event.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!--project artifact-->
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>biz-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--end project artifact-->

        <!-- sofa-ark-plugin -->
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>healthcheck-sofa-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>runtime-sofa-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofa-ark-spi</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- end sofa-ark-plugin -->

        <!-- tools -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- end Tools -->

        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>${module.install.skip}</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>${module.deploy.skip}</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>${skipTests}</skipTests>
                    <includes>
                        <!-- 这里需要根据自己的需要指定要跑的单元测试 -->
                        <include>**/*Test.java</include>
                    </includes>
                    <!-- 如无特殊需求，将forkMode设置为once -->
                    <forkMode>once</forkMode>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>

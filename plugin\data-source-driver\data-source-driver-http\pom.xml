<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.mingdao.edge.plugin</groupId>
        <artifactId>data-source-driver</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>data-source-driver-http</artifactId>
    <name>${artifactId}</name>
    <version>${data-source-driver-http.version}</version>
    <packaging>jar</packaging>

    <properties>
    </properties>

    <dependencies>
        <!-- project -->
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common-biz-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.plugin</groupId>
            <artifactId>data-source-driver-api</artifactId>
            <version>${data-source-driver-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common-http</artifactId>
        </dependency>
        <!-- end project -->
        <!-- sofa-ark-plugin -->
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>healthcheck-sofa-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>runtime-sofa-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofa-ark-spi</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- end sofa-ark-plugin -->
        <!-- tools -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- end tools -->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-ark-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-cli</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <skipArkExecutable>true</skipArkExecutable>
                    <outputDirectory>${user.dir}/download</outputDirectory>
                    <bizName>${artifactId}</bizName>
                    <webContextPath>${artifactId}</webContextPath>
                    <declaredMode>true</declaredMode>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>

package com.mingdao.edge.plugin.data.source.driver.mssql.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mingdao.edge.plugin.data.source.driver.mssql.service.MSSQLMyBatisService;
import com.mingdao.edge.plugin.data.source.driver.mssql.service.impl.DataCollectorImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MSSQL MyBatis Controller
 * 演示如何使用类似gateway模块DataBaseServiceImpl的MyBatis方式执行SQL
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@RestController
@RequestMapping("/mssql/mybatis")
public class MSSQLMyBatisController {

    @Autowired
    private MSSQLMyBatisService mssqlMyBatisService;

    @Autowired
    private com.mingdao.edge.plugin.data.source.driver.mssql.mapper.DynamicMssqlMapper dynamicMssqlMapper;
    @Autowired
    private DataCollectorImpl dataDownload;

    /**
     * 使用MyBatis查询单个记录
     * @param entryId 主键ID
     * @return 查询结果
     */
    @GetMapping("/selectOne/{entryId}")
    public Map<String, Object> selectOneById(@PathVariable Integer entryId) {
        log.info("MyBatis查询单个记录: {}", entryId);

        try {
            String sql = "SELECT FEntryID, FBillNo, FNumber, FName, FAuxQty, FBomInputAuxQty, FAuxQtyPick, lastDate, ERPID " +
                        "FROM A_A_TEST WHERE FEntryID = #{params.entryId}";

            JSONObject params = new JSONObject();
            params.put("entryId", entryId);

            Map<String, Object> result = mssqlMyBatisService.selectOne(sql, params);

            //DbPageQueryDto dto = new DbPageQueryDto();
            //dto.setSql(sql);
            //dto.setSize(20);
            //dto.setPage(1);
            //dto.setParams(params);
            //JSONObject r = dataDownload.execute(dto);


            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", result);
            response.put("message", result != null ? "查询成功" : "未找到记录");

            return response;

        } catch (Exception e) {
            log.error("MyBatis查询单个记录失败: {}", entryId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 使用MyBatis查询记录列表
     * @param name 名称关键字（可选）
     * @param minQty 最小数量（可选）
     * @return 查询结果
     */
    @GetMapping("/select")
    public Map<String, Object> selectList(@RequestParam(required = false) String name,
                                         @RequestParam(required = false) Integer minQty) {
        log.info("MyBatis查询记录列表: name={}, minQty={}", name, minQty);

        try {
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT FEntryID, FBillNo, FNumber, FName, FAuxQty, FBomInputAuxQty, FAuxQtyPick, lastDate, ERPID ");
            sqlBuilder.append("FROM A_A_TEST WHERE 1=1 ");

            Map<String, Object> params = new HashMap<>();

            if (name != null && !name.trim().isEmpty()) {
                sqlBuilder.append("AND FName LIKE #{params.name} ");
                params.put("name", "%" + name.trim() + "%");
            }

            if (minQty != null) {
                sqlBuilder.append("AND FAuxQty >= #{params.minQty} ");
                params.put("minQty", minQty);
            }

            sqlBuilder.append("ORDER BY FEntryID");

            List<Map<String, Object>> result = mssqlMyBatisService.select(sqlBuilder.toString(), params);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", result);
            response.put("total", result.size());
            response.put("message", "查询成功");

            return response;

        } catch (Exception e) {
            log.error("MyBatis查询记录列表失败: name={}, minQty={}", name, minQty, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 使用MyBatis分页查询
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param name 名称关键字（可选）
     * @return 分页查询结果
     */
    @GetMapping("/selectPage")
    public Map<String, Object> selectPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                         @RequestParam(defaultValue = "10") Integer pageSize,
                                         @RequestParam(required = false) String name) {
        log.info("MyBatis分页查询: pageNum={}, pageSize={}, name={}", pageNum, pageSize, name);

        try {
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT FEntryID, FBillNo, FNumber, FName, FAuxQty, FBomInputAuxQty, FAuxQtyPick, lastDate, ERPID ");
            sqlBuilder.append("FROM A_A_TEST WHERE 1=1 ");

            Map<String, Object> params = new HashMap<>();

            if (name != null && !name.trim().isEmpty()) {
                sqlBuilder.append("AND FName LIKE #{params.name} ");
                params.put("name", "%" + name.trim() + "%");
            }

            sqlBuilder.append("ORDER BY FEntryID");

            IPage<Map<String, Object>> result = mssqlMyBatisService.selectPageBySql(sqlBuilder.toString(), pageNum, pageSize, params);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", result.getRecords());
            response.put("total", result.getTotal());
            response.put("pageNum", result.getCurrent());
            response.put("pageSize", result.getSize());
            response.put("totalPages", result.getPages());
            response.put("message", "分页查询成功");

            return response;

        } catch (Exception e) {
            log.error("MyBatis分页查询失败: pageNum={}, pageSize={}, name={}", pageNum, pageSize, name, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "分页查询失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 使用MyBatis插入记录
     * @param data 插入数据
     * @return 操作结果
     */
    @PostMapping("/insert")
    public Map<String, Object> insert(@RequestBody Map<String, Object> data) {
        log.info("MyBatis插入记录: {}", data);

        try {
            String sql = "INSERT INTO A_A_TEST (FBillNo, FNumber, FName, FAuxQty, FBomInputAuxQty, FAuxQtyPick, lastDate, ERPID) " +
                        "VALUES (#{params.billNo}, #{params.number}, #{params.name}, #{params.auxQty}, " +
                        "#{params.bomInputAuxQty}, #{params.auxQtyPick}, #{params.lastDate}, #{params.erpId})";

            Map<String, Object> params = new HashMap<>();
            params.put("billNo", data.get("billNo"));
            params.put("number", data.get("number"));
            params.put("name", data.get("name"));
            params.put("auxQty", data.get("auxQty"));
            params.put("bomInputAuxQty", data.get("bomInputAuxQty"));
            params.put("auxQtyPick", data.get("auxQtyPick"));
            params.put("lastDate", data.get("lastDate"));
            params.put("erpId", data.get("erpId"));

            Long generatedId = mssqlMyBatisService.insert(sql, params);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("generatedId", generatedId);
            response.put("message", "插入成功");

            return response;

        } catch (Exception e) {
            log.error("MyBatis插入记录失败: {}", data, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "插入失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 使用MyBatis更新记录
     * @param entryId 主键ID
     * @param data 更新数据
     * @return 操作结果
     */
    @PutMapping("/update/{entryId}")
    public Map<String, Object> update(@PathVariable Integer entryId, @RequestBody Map<String, Object> data) {
        log.info("MyBatis更新记录: ID={}, Data={}", entryId, data);

        try {
            String sql = "UPDATE A_A_TEST SET FBillNo=#{params.billNo}, FNumber=#{params.number}, FName=#{params.name}, " +
                        "FAuxQty=#{params.auxQty}, FBomInputAuxQty=#{params.bomInputAuxQty}, FAuxQtyPick=#{params.auxQtyPick}, " +
                        "lastDate=#{params.lastDate}, ERPID=#{params.erpId} WHERE FEntryID=#{params.entryId}";

            Map<String, Object> params = new HashMap<>();
            params.put("entryId", entryId);
            params.put("billNo", data.get("billNo"));
            params.put("number", data.get("number"));
            params.put("name", data.get("name"));
            params.put("auxQty", data.get("auxQty"));
            params.put("bomInputAuxQty", data.get("bomInputAuxQty"));
            params.put("auxQtyPick", data.get("auxQtyPick"));
            params.put("lastDate", data.get("lastDate"));
            params.put("erpId", data.get("erpId"));

            int affectedRows = mssqlMyBatisService.update(sql, params);

            Map<String, Object> response = new HashMap<>();
            response.put("success", affectedRows > 0);
            response.put("affectedRows", affectedRows);
            response.put("message", affectedRows > 0 ? "更新成功" : "更新失败，可能记录不存在");

            return response;

        } catch (Exception e) {
            log.error("MyBatis更新记录失败: ID={}", entryId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "更新失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 使用MyBatis删除记录
     * @param entryId 主键ID
     * @return 操作结果
     */
    @DeleteMapping("/delete/{entryId}")
    public Map<String, Object> delete(@PathVariable Integer entryId) {
        log.info("MyBatis删除记录: ID={}", entryId);

        try {
            String sql = "DELETE FROM A_A_TEST WHERE FEntryID=#{params.entryId}";

            Map<String, Object> params = new HashMap<>();
            params.put("entryId", entryId);

            int affectedRows = mssqlMyBatisService.delete(sql, params);

            Map<String, Object> response = new HashMap<>();
            response.put("success", affectedRows > 0);
            response.put("affectedRows", affectedRows);
            response.put("message", affectedRows > 0 ? "删除成功" : "删除失败，可能记录不存在");

            return response;

        } catch (Exception e) {
            log.error("MyBatis删除记录失败: ID={}", entryId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "删除失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 使用MyBatis执行COUNT查询
     * @param name 名称关键字（可选）
     * @return 查询结果
     */
    @GetMapping("/count")
    public Map<String, Object> count(@RequestParam(required = false) String name) {
        log.info("MyBatis COUNT查询: name={}", name);

        try {
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT FEntryID FROM A_A_TEST WHERE 1=1 ");

            Map<String, Object> params = new HashMap<>();

            if (name != null && !name.trim().isEmpty()) {
                sqlBuilder.append("AND FName LIKE #{params.name} ");
                params.put("name", "%" + name.trim() + "%");
            }

            long count = mssqlMyBatisService.count(sqlBuilder.toString(), params);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("count", count);
            response.put("message", "COUNT查询成功");

            return response;

        } catch (Exception e) {
            log.error("MyBatis COUNT查询失败: name={}", name, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "COUNT查询失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 检查记录是否存在
     * @param entryId 主键ID
     * @return 检查结果
     */
    @GetMapping("/exists/{entryId}")
    public Map<String, Object> exists(@PathVariable Integer entryId) {
        log.info("MyBatis检查记录是否存在: ID={}", entryId);

        try {
            boolean exists = mssqlMyBatisService.exists("A_A_TEST", "FEntryID", entryId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("exists", exists);
            response.put("message", "检查完成");

            return response;

        } catch (Exception e) {
            log.error("MyBatis检查记录是否存在失败: ID={}", entryId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "检查失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 测试分页查询
     * @return 测试结果
     */
    @GetMapping("/testPage")
    public Map<String, Object> testPage() {
        log.info("测试MyBatis分页查询");

        try {
            String sql = "SELECT FEntryID, FBillNo, FName, FAuxQty FROM A_A_TEST ORDER BY FEntryID";
            Map<String, Object> params = new HashMap<>();

            log.info("开始执行分页查询，SQL: {}", sql);
            IPage<Map<String, Object>> result = mssqlMyBatisService.selectPageBySql(sql, 1, 5, params);
            log.info("分页查询完成，结果类型: {}", result.getClass().getName());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", result.getRecords());
            response.put("total", result.getTotal());
            response.put("pageNum", result.getCurrent());
            response.put("pageSize", result.getSize());
            response.put("totalPages", result.getPages());
            response.put("hasNext", result.getCurrent() < result.getPages());
            response.put("hasPrevious", result.getCurrent() > 1);
            response.put("message", "分页测试成功");

            return response;

        } catch (Exception e) {
            log.error("分页测试失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "分页测试失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            response.put("stackTrace", java.util.Arrays.toString(e.getStackTrace()));
            return response;
        }
    }

    /**
     * 直接测试Mapper分页查询
     * @return 测试结果
     */
    @GetMapping("/testMapperPage")
    public Map<String, Object> testMapperPage() {
        log.info("直接测试Mapper分页查询");

        try {
            String sql = "SELECT FEntryID, FBillNo, FName, FAuxQty FROM A_A_TEST ORDER BY FEntryID";
            Map<String, Object> params = new HashMap<>();

            // 创建分页对象
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<Map<String, Object>> page =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1, 5);

            log.info("开始直接调用Mapper分页查询，SQL: {}", sql);
            IPage<Map<String, Object>> result = dynamicMssqlMapper.selectPageBySql(page, sql, params);
            log.info("Mapper分页查询完成，结果类型: {}", result.getClass().getName());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", result.getRecords());
            response.put("total", result.getTotal());
            response.put("pageNum", result.getCurrent());
            response.put("pageSize", result.getSize());
            response.put("totalPages", result.getPages());
            response.put("message", "Mapper分页测试成功");

            return response;

        } catch (Exception e) {
            log.error("Mapper分页测试失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Mapper分页测试失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            response.put("stackTrace", java.util.Arrays.toString(e.getStackTrace()));
            return response;
        }
    }

    /**
     * 获取服务状态
     * @return 服务状态
     */
    @GetMapping("/status")
    public Map<String, Object> getStatus() {
        Map<String, Object> response = new HashMap<>();
        try {
            response.put("initialized", mssqlMyBatisService.isServiceInitialized());
            response.put("configInfo", mssqlMyBatisService.getConfigInfo());
            response.put("mapperAvailable", mssqlMyBatisService != null);
            response.put("message", "服务状态获取成功");
        } catch (Exception e) {
            response.put("initialized", false);
            response.put("error", e.getMessage());
            response.put("message", "服务状态获取失败");
        }
        return response;
    }
}

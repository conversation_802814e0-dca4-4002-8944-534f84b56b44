package com.mingdao.edge.core.pulsar.vo;

import lombok.Data;
import org.apache.pulsar.client.api.MessageListener;
import org.apache.pulsar.client.api.SubscriptionType;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class PulsarSubscriptionConfig {

    /**
     * 主题名称
     */
    private String topic;

    /**
     * 订阅名称
     */
    private String subscriptionName;

    /**
     * 订阅类型
     */
    private SubscriptionType subscriptionType = SubscriptionType.Shared;

    /**
     * 消费者名称
     */
    private String consumerName;

    /**
     * 接收队列大小
     */
    private int receiverQueueSize = 1000;

    /**
     * 消息监听器
     */
    private MessageListener<byte[]> messageListener;
}

package com.mingdao.edge.plugin.data.source.driver.mssql.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 动态MSSQL Mapper接口
 * 类似gateway模块的DynamicSqlMapper，但专门用于动态MSSQL数据源
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Mapper
public interface DynamicMssqlMapper {

    /**
     * 执行查询SQL
     * @param sql SQL语句（使用MyBatis参数格式，如：SELECT * FROM table WHERE name = #{params.name}）
     * @param params SQL参数
     * @return 查询结果列表
     */
    List<Map<String, Object>> selectBySql(@Param("sql") String sql, @Param("params") Map<String, Object> params);

    /**
     * 执行分页查询SQL
     * @param page 分页对象（必须是第一个参数）
     * @param sql SQL语句（使用MyBatis参数格式）
     * @param params SQL参数
     * @return 分页查询结果
     */
    IPage<Map<String, Object>> selectPageBySql(Page<Map<String, Object>> page,
                                              @Param("sql") String sql,
                                              @Param("params") Map<String, Object> params);

    /**
     * 执行插入SQL
     * @param sql SQL语句（使用MyBatis参数格式）
     * @param params SQL参数
     * @return 影响行数
     */
    int insertBySql(@Param("sql") String sql, @Param("params") Map<String, Object> params);

    /**
     * 执行更新SQL
     * @param sql SQL语句（使用MyBatis参数格式）
     * @param params SQL参数
     * @return 影响行数
     */
    int updateBySql(@Param("sql") String sql, @Param("params") Map<String, Object> params);

    /**
     * 执行删除SQL
     * @param sql SQL语句（使用MyBatis参数格式）
     * @param params SQL参数
     * @return 影响行数
     */
    int deleteBySql(@Param("sql") String sql, @Param("params") Map<String, Object> params);

    /**
     * 根据ID列表批量删除
     * @param tableName 表名
     * @param ids ID列表
     * @return 影响行数
     */
    int deleteByIds(@Param("tableName") String tableName, @Param("ids") Collection<Long> ids);

    /**
     * 执行COUNT查询
     * @param sql SQL语句（使用MyBatis参数格式）
     * @param params SQL参数
     * @return 查询结果数量
     */
    long countBySql(@Param("sql") String sql, @Param("params") Map<String, Object> params);

    /**
     * 检查表是否存在
     * @param tableName 表名
     * @return 是否存在
     */
    int checkTableExists(@Param("tableName") String tableName);

    /**
     * 获取表结构信息
     * @param tableName 表名
     * @return 表结构信息
     */
    List<Map<String, Object>> getTableStructure(@Param("tableName") String tableName);
}

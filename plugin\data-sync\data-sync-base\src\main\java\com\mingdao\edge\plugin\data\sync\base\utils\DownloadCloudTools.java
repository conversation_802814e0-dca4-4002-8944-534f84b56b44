package com.mingdao.edge.plugin.data.sync.base.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.plugin.api.data.sync.dto.ColTemp;
import lombok.extern.slf4j.Slf4j;

import java.sql.Clob;
import java.util.Date;
import java.util.Map;

@Slf4j
/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public class DownloadCloudTools {
    private static final String startTag = "${";
    private static final String endTag = "}";

    public static String changeParam(String url, JSONObject re, Map<String, String> map) {
        if (StrUtil.isEmpty(url)) {
            return "";
        }

        String param;//变量名
        String table = "";
        String paramValue = null;//参数值
        String[] temp;
        String tempStr;
        while (url.indexOf(startTag) != -1) {
            param = url.substring(url.indexOf(startTag) + 2, url.indexOf(endTag, url.indexOf(startTag)));
            paramValue = "";
            if ("API_TODAY".equals(param)) {
                paramValue = DateUtil.format(new Date(), DatePattern.NORM_DATE_FORMAT);
            } else if ("API_AUTHORITYCODE".equals(param)) {
                //paramValue = InitParam.API_AUTHORITYCODE;
            } else if ("SUPPLIESRECORD_FROB".equals(param)) {
                if (re.getInteger("TYPE") == 0) {
                    paramValue = "1";    //TYPE = 0 是 备料；    TYPE = 1 是 退料；
                } else {
                    paramValue = "0";
                }

//            } else if("guid".equals(param)){
//                paramValue = "" + re.getLong("AUTOID");
            } else if (re.containsKey(param)) {
                paramValue = re.getString(param);
            } else if (map.containsKey(param)) {
                paramValue = "" + map.get(param);
            }
            //转换成url
            //设置变量
            url = url.replace(startTag + param + endTag, paramValue.replace("\\", "\\\\").replace("\"", "\\\""));//防止json格式错误。
        }
        return url;
    }

    public static JSONObject urlParamsToJSONObject(String urlParams) {
        JSONObject jsonObject = new JSONObject();
        if (StrUtil.isEmpty(urlParams)) {
            return jsonObject;
        }
        String[] pairs = urlParams.split("&");
        for (String pair : pairs) {
            int idx = pair.indexOf("=");
            if (idx > -1) {
                String key = pair.substring(0, idx);
                if (key.startsWith("MD")) {
                    continue;
                }
                String value = pair.substring(idx + 1);
                // URL解码
                try {
                    value = java.net.URLDecoder.decode(value, "UTF-8");
                } catch (Exception e) {
                    // 解码失败则保留原值
                }
                jsonObject.put(key, value);
            } else if (!pair.isEmpty()) {
                jsonObject.put(pair, "");
            }
        }
        return jsonObject;
    }

    /**
     * 根据入库方案转换数据字段名称
     *
     * @param warehouseProject 入库方案
     * @param dataItem         数据
     * @return
     */
    public static JSONObject convertInStockParams(JSONObject warehouseProject, JSONObject dataItem, Map<String, ColTemp> colTempMap) {
        JSONObject result = new JSONObject();
        if (!warehouseProject.containsKey("TRANSFERFIELD")) {
            log.warn("入库方案编码[ {} ]对应的参数 TRANSFERFIELD 为空（传输配置未进行配置）", warehouseProject.getString("CODE"));
            return result;
        }
        String TRANSFERFIELD =  warehouseProject.getString("TRANSFERFIELD");
        JSONArray transferField = JSON.parseArray(TRANSFERFIELD);
        for (int i = 0; i < transferField.size(); i++){
            JSONObject field = (JSONObject) transferField.get(i);
            String sourceField = field.getString("sourceField");
            // targetField是我们的字段
            String targetField = field.getString("targetField");
            Object paramValue = null;
            if (StrUtil.isBlank(targetField)) {
                // 没有关联我们的字段，则设为默认值
                paramValue = field.get("defaultValue");
            } else {
                // 获取数据值
                paramValue = dataItem.getString(targetField);
                if (ObjectUtil.isEmpty(paramValue)) {
                    // 数据无值，获取默认值
                    paramValue = field.get("defaultValue");
                }
            }
            Object value = paramValue;
            ColTemp fieldTemp = colTempMap.get(targetField);
            if (fieldTemp != null) {
                value = fieldTemp.convertFieldType(paramValue);
            }

            result.put(sourceField, value);
        }
        return result;
    }

    public static JSONObject convertDataToCustomerDataType(JSONObject data, Map<String, ColTemp> colTempMap) {
        JSONObject result = new JSONObject();
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            String fieldKey = entry.getKey();
            if (fieldKey.startsWith("MD")) {
                continue;
            }
            Object value = data.get(fieldKey);
            result.put(fieldKey, value);
            ColTemp colTemp = colTempMap.get(fieldKey);
            if (colTemp != null) {
                if (Boolean.TRUE.equals(colTemp.getIsNumber())) {
                    if (value instanceof String) {
                        String strValue = (String) value;
                        if (StrUtil.isBlank(strValue)) {
                            result.put(fieldKey, null);
                        } else {
                            if ("Double".equalsIgnoreCase(colTemp.getDe_df_code())) {
                                value = Double.parseDouble(strValue);
                            } else if ("Integer".equalsIgnoreCase(colTemp.getDe_df_code())) {
                                value = Integer.parseInt(strValue);
                            }
                            result.put(fieldKey, value);
                        }
                    }
                }
            }
        }
        return result;
    }

    public static void convertClobToString(Map<String, Object> item) {
        for (Map.Entry<String, Object> entry : item.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof Clob) {
                try {
                    Clob clob = (Clob) value;
                    String str = clob.getSubString(1, (int) clob.length());
                    entry.setValue(str);
                } catch (Exception e) {
                    log.error("Clob字段转String异常: {}", e.getMessage(), e);
                }
            }
        }
    }
}

package com.mingdao.edge.plugin.api.gateway.dto;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 边缘指标
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EdgeState implements Serializable {

    /**
     * 指标名称
     */
    private String name;
    /**
     * 指标key
     */
    private String key;
    /**
     * 业务名称
     */
    private String bizName;
    /**
     * 指标值
     */
    private String value;
    /**
     * 指标时间
     */
    private Date time;
    /**
     * 是否可修改
     */
    private Boolean editable;
    /**
     * sysOrg
     */
    private Long sysOrg;

    public String getCacheKey() {
        return StrUtil.format("{}_{}", key, bizName);
    }

}

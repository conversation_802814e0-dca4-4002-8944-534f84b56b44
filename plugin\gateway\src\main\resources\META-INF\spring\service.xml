<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:sofa="http://sofastack.io/schema/sofaboot"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://sofastack.io/schema/sofaboot http://sofastack.io/schema/sofaboot.xsd">

    <!--<sofa:service interface="com.mingdao.edge.plugin.api.mapper.DataLogMapper" ref="DataLogMapper">-->
    <!--    <sofa:binding.jvm serialize="true"/>-->
    <!--</sofa:service>-->
    <!--<sofa:service interface="com.mingdao.edge.plugin.api.mapper.DataLogMsgMapper" ref="DataLogMsgMapper">-->
    <!--    <sofa:binding.jvm serialize="true"/>-->
    <!--</sofa:service>-->


</beans>
